import { MasterKpiService } from './../../../services/master-kpi.service';
import { AfterViewInit, Component, EventEmitter, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { AccountService } from "src/app/services/account.service";
import {
  DecimalDigitEnum,
  ExportTypeEnum,
  FinancialValueUnitsEnum,
  MiscellaneousService,
  OrderTypesEnum,
  PeriodTypeQuarterEnum,
  ErrorMessage
} from "src/app/services/miscellaneous.service";
import { ActionsEnum, FeaturesEnum, UserSubFeaturesEnum, PermissionService, KPIModulesEnum } from "src/app/services/permission.service";
import { Observable, Subject, Subscription } from 'rxjs';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { AuditService } from "src/app/services/audit.service";
import { Table } from 'primeng/table';
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { filter } from 'rxjs/operators';
import { NumberDecimalConst, FinancialsSubTabs, KpiTypes, PeriodTypeFilterOptions, PeriodType, FinancialsValueTypes, ImpactKPIConstants, CreditKpiConstants, GlobalConstants, KpiTypesConstants } from "src/app/common/constants";
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { isNil } from 'src/app/utils/utils';
import { Audit, MappedDocuments, TableHeader } from '../../file-uploads/kpi-cell-edit/kpiValueModel';
import { extractDateComponents } from '../../file-uploads/kpi-cell-edit/cell-edit-utils';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { DatePipe } from '@angular/common';
import { getConversionErrorMessage } from 'src/app/utils/utils';

@Component({
  selector: 'app-other-kpi',
  templateUrl: './other-kpi.component.html',
  styleUrls: ['./master-kpi-beta.component.scss']
})
export class OtherKpiComponent implements OnInit, AfterViewInit {
  NumberDecimalConst = NumberDecimalConst;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  exportType: typeof ExportTypeEnum = ExportTypeEnum;
  searchFilter: any = null;
  private eventsSubscription: Subscription;
  @Input() events: Observable<void>;
  @Input() modelList: any;
  @Input() tradingRecordPermissions: any = [];
  @Input() creditKPIPermissions: any = [];
  @Input() otherKPI1Permission: any = [];
  @Input() otherKPI2Permission: any = [];
  @Input() otherKPI3Permission: any = [];
  @Input() otherKPI4Permission: any = [];
  @Input() otherKPI5Permission: any = [];
  @Input() otherKPI6Permission: any = [];
  @Input() otherKPI7Permission: any = [];
  @Input() otherKPI8Permission: any = [];
  @Input() otherKPI9Permission: any = [];
  @Input() otherKPI10Permission: any = [];
  dataTable: any;
  message: any;
  id: any;
  masterKPIs: any[];
  msgTimeSpan: number;
  loading = false;
  masterKpiValueUnit: any;
  modalOption: NgbModalOptions = {};
  currentModelRef: any;
  globalFilter: string = "";
  tableReload = false;
  isLoader: boolean = false;
  portfolioInfoSectionModel: any = {};
  frozenCols: any = [{ field: "KPI", header: "KPI" }];
  objMasterKPIList: any = [];
  masterKPICols: any = [];
  modelMasterKpi: any = {};
  portfolioCompanyMasterKPIValuesList: any[];
  portfolioCompanyMasterKPIValuesListClone: any[];
  financialKpiSearchFilter: any;
  expandedMasterKPIs: any[] = [];
  totalMasterKPIValuesRecords: number;
  financialPeriodErrorMessage: string = "";
  financialKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  unitOfCurrency: string = FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions];
  auditToggle: boolean = false;
  @ViewChild('dt') dt: Table | undefined;
  updateModel: any = {};
  confirmUpdate = false;
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  infoUpdate: boolean = false;
  ErrorNotation: boolean = false;
  isToasterMessage = false;
  ModuleName: string = "";
  @ViewChild('menu') uiuxMenu!: MatMenu;
  @ViewChild('masterMenuTrigger') menuTrigger: MatMenuTrigger;
  ModuleCurrency: string;
  @Input() kpiName: string;
  exportMasterKPILoading: boolean = false;
  isValueUpdated: boolean = false;
  tabValueTypeList: ITab[] = [];
  IsPageLoad: boolean = true;
  tabName: string = "";
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  filterOptions: any[] = [];
  tableColumns = [];
  tableFrozenColumns = [];
  tableResult = [];
  tableResultClone = [];
  kpiFilterCols: any = [];
  auditLogList: any = [];
  isToggleChecked: boolean = false;
  isYtd: boolean = false;
  isLtm: boolean = false;
  hasYtd: boolean = false;
  hasLtm: boolean = false;
  isYtdPageLoad: boolean = true;
  isLtmPageLoad: boolean = true;
  @Input() pageConfigData = [{ kpiConfigurationData: [], hasChart: false, kpiType: "" }];
  subSectionFields = [];
  pageConfigResponse = { kpiConfigurationData: [], hasChart: false, kpiType: "" };
  defaultType: string = "Monthly";
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: any;
  dataRow: object;
  dataColumns: TableHeader;
  valueTypeString: string;
  kpiCurrencyFilterModel: any = {};
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
  auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;
  constructor(
    private accountService: AccountService,
    private miscService: MiscellaneousService,
    private portfolioCompanyService: PortfolioCompanyService,
    private _avRoute: ActivatedRoute,
    private masterKpiService: MasterKpiService,
    private router: Router,
    private toastrService: ToastrService,
    private permissionService: PermissionService,
    private auditService: AuditService,
    private identityService: OidcAuthService,
    private datePipe: DatePipe
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }

    this.modelMasterKpi.periodType = {
      type: PeriodTypeQuarterEnum.Last1Year,
    };
    this.modelMasterKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelMasterKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    };

    this.masterKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }
  sourceURL: any;
  ngAfterViewInit() {
    if (this.uiuxMenu != undefined) {
      (this.uiuxMenu as any).closed = this.uiuxMenu.closed
      this.configureMenuClose(this.uiuxMenu.closed);
    }
  }
  configureMenuClose(old: MatMenu['closed']): MatMenu['closed'] {
    const upd = new EventEmitter();
    feed(upd.pipe(
      filter(event => {
        if (event === 'click') {
          return false;
        }
        return true;
      }),
    ), old);
    return upd;
  }
  ngOnInit() {
    this.pageConfigResponse = this.getKpiConfiguration();
    this.subSectionFields = this.pageConfigResponse?.kpiConfigurationData;
    this.getValueTypeTabList();
    this.getPortfolioCompanyMasterKPIValues(null);
    this.eventsSubscription = this.events?.subscribe((res) => {
      this.searchFilter = res;
      this.getPortfolioCompanyMasterKPIValues(null);
    });
    this.msgTimeSpan = this.miscService.getMessageTimeSpan();
  }
  
  getKpiConfiguration(){
    if (!this.modelList?.moduleId || !this.pageConfigData) {
      return undefined;
    }
  
    // Mapping of module IDs to KPI types
    const kpiTypeMap: Record<string, string> = {
      [KPIModulesEnum.TradingRecords]: KpiTypes.TradingRecordsBeta.type,
      [KPIModulesEnum.CreditKPI]: KpiTypes.Credit.type,
      [KPIModulesEnum.CustomTable1]: KpiTypes.CustomTable1.type,
      [KPIModulesEnum.CustomTable2]: KpiTypes.CustomTable2.type,
      [KPIModulesEnum.CustomTable3]: KpiTypes.CustomTable3.type,
      [KPIModulesEnum.CustomTable4]: KpiTypes.CustomTable4.type,
      [KPIModulesEnum.OtherKPI1]: KpiTypes.OtherKPI1.type,
      [KPIModulesEnum.OtherKPI2]: KpiTypes.OtherKPI2.type,
      [KPIModulesEnum.OtherKPI3]: KpiTypes.OtherKPI3.type,
      [KPIModulesEnum.OtherKPI4]: KpiTypes.OtherKPI4.type,
      [KPIModulesEnum.OtherKPI5]: KpiTypes.OtherKPI5.type,
      [KPIModulesEnum.OtherKPI6]: KpiTypes.OtherKPI6.type,
      [KPIModulesEnum.OtherKPI7]: KpiTypes.OtherKPI7.type,
      [KPIModulesEnum.OtherKPI8]: KpiTypes.OtherKPI8.type,
      [KPIModulesEnum.OtherKPI9]: KpiTypes.OtherKPI9.type,
      [KPIModulesEnum.OtherKPI10]: KpiTypes.OtherKPI10.type,
    };
  
    const kpiType = kpiTypeMap[this.modelList.moduleId];
    return this.pageConfigData.find(config => config.kpiType === kpiType);
  }
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
  getBetaMasterKPIValues(searchFilter: any, event: any) {
    this.financialKpiSearchFilter = searchFilter;
    this.masterKpiService
      .getBetaMasterKPIValues({
        portfolioCompanyID: this.modelList?.portfolioCompanyID,
        paginationFilter: event,
        searchFilter: searchFilter,
        valueType:
          this.valueTypeString != undefined
            ? this.valueTypeString
            : this.tabValueTypeList.length == 0
              ? ImpactKPIConstants.Actual
              : this.tabName,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: this.IsPageLoad,
        moduleId: this.modelList?.moduleId,
        companyId: this.modelList?.portfolioCompanyID?.toString(),
        kpiConfigurationData: this.pageConfigResponse?.kpiConfigurationData,
        isYtdPageLoad: this.isYtdPageLoad,
        isLtmPageLoad: this.isLtmPageLoad,
        isYtd: this.isYtd,
        isLtm: this.isLtm,
        IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
        SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
        currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
        reportingCurrencyCode: this.modelList?.reportingCurrencyDetail?.currencyCode,
        currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
      })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.loading = false;
            this.ErrorNotation = false;
            this.isLoader = false;
            this.tableReload = true;
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.auditLogList = result?.companyKpiAuditLog || [];
            this.tableResultClone = result?.rows || [];
            this.convertUnits();
            this.kpiFilterCols = [...this.tableFrozenColumns, ...this.tableColumns];
            this.IsPageLoad = false;
            if (this.isYtd) this.isYtdPageLoad = false;
            if (this.isLtm) this.isLtmPageLoad = false;
            if (result != null) {
              this.isMonthly = result?.isMonthly;
              this.isQuarterly = result?.isQuarterly;
              this.isAnnually = result?.isAnnually;
              this.SetFilterOptionsKeys(result);
            }
          } else {
            this.clearData();
          }
        },
        error: (error) => {
          this.clearData();
        }
      });
  }

  private SetFilterOptionsKeys(result: any) {
    this.filterOptions?.forEach(element => {
      switch (element.field) {
        case PeriodTypeFilterOptions.Monthly:
          element.key = result?.isMonthly;
          break;
        case PeriodTypeFilterOptions.Quarterly:
          element.key = result?.isQuarterly;
          break;
        case PeriodTypeFilterOptions.Annual:
          element.key = result?.isAnnually;
          break;
      }
    });
    this.setDefaultTypeTab();
  }

  getPortfolioCompanyMasterKPIValues(event: any) {
    this.isLoader = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "FinancialKPI.KPI",
        multiSortMeta: this.financialKPIMultiSortMeta,
        sortOrder: -1,
      };
    }
    let searchFilter = this.searchFilter;
    if (searchFilter == null) {
      let sortOrder =
        this.modelMasterKpi.orderType.type == OrderTypesEnum.LatestOnRight
          ? [
            { field: "year", order: 1 },
            { field: "quarter", order: 1 },
          ]
          : [
            { field: "year", order: -1 },
            { field: "quarter", order: -1 },
          ];
      searchFilter = {
        sortOrder: sortOrder,
        periodType: this.modelMasterKpi.periodType.type,
      };

      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            this.modelMasterKpi.startPeriod.getFullYear(),
            this.modelMasterKpi.startPeriod.getMonth(),
            this.modelMasterKpi.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            this.modelMasterKpi.endPeriod.getFullYear(),
            this.modelMasterKpi.endPeriod.getMonth(),
            this.modelMasterKpi.endPeriod.getDate()
          )
        );
      }
    } else {
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            searchFilter.startPeriod.getFullYear(),
            searchFilter.startPeriod.getMonth(),
            searchFilter.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            searchFilter.endPeriod.getFullYear(),
            searchFilter.endPeriod.getMonth(),
            searchFilter.endPeriod.getDate()
          )
        );
      }
    }
    this.getBetaMasterKPIValues(searchFilter, event)
  }
  clearData() {
    this.loading = false;
    this.isLoader = false;
    this.tableColumns = [];
    this.tableResult = [];
    this.tableResultClone = [];
    this.auditLogList = [];
    this.IsPageLoad = false;
  }
  convertUnits() {
    this.tableResult = [];
    let local = this;
    let masterValueUnit = this.masterKpiValueUnit;
    this.tableResultClone.forEach(function (
      value: any
    ) {
      let valueClone = JSON.parse(JSON.stringify(value));
      if (valueClone["KPI Info"] != "%" && valueClone["KPI Info"] != "x" && valueClone["KPI Info"] != "#" &&
        valueClone["KPI Info"] != "Text" && valueClone["KPI Info"] != ""
      ) {
        switch (Number(masterValueUnit.typeId)) {
          case FinancialValueUnitsEnum.Absolute:
            break;
          case FinancialValueUnitsEnum.Thousands:
            valueClone = local.conversionValue(valueClone, local, 1000);
            break;
          case FinancialValueUnitsEnum.Millions:
            valueClone = local.conversionValue(valueClone, local, 1000000);
            break;
          case FinancialValueUnitsEnum.Billions:
            valueClone = local.conversionValue(valueClone, local, 1000000000);
            break;
        }
      }
      local.tableResult.push(valueClone)
    });
  }
  conversionValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] =
          !isNil(valueClone[col.field]) ? !isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field])))
            ? (valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field]) / value
            : valueClone[col.field] : valueClone[col.field];
      }
    });
    return valueClone;
  }
  kpiTable_GlobalFilter(event) {
    this.masterKpiValueUnit = event?.UnitType == undefined ? {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    } : event?.UnitType;
    this.searchFilter = event;
    this.kpiCurrencyFilterModel = event;
    if(this.modelList?.reportingCurrencyDetail?.currencyCode != null && event?.currencyCode != null && this.modelList?.reportingCurrencyDetail?.currencyCode != event?.currencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);
    this.menuTrigger.closeMenu();
  }

  //#region New Code
  onChangePeriodOption(type) {
    this.filterOptions.forEach((x) => (x.key = false));
    if (type?.field == "Monthly") {
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if (type?.field == "Quarterly") {
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
    } else {
      this.isMonthly = false;
      this.isQuarterly = false;
      if (type != undefined)
        type.key = this.isAnnually = true;
    }
    this.setDefaultTypeTab();
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  setDefaultTypeTab = () => {
    if (this.isMonthly)
      this.defaultType = PeriodTypeFilterOptions.Monthly;
    else if (this.isQuarterly)
      this.defaultType = PeriodTypeFilterOptions.Quarterly;
    else
      this.defaultType = PeriodTypeFilterOptions.Annual;
  }
  selectValueTab(tab: ITab) {
    this.modelList.moduleId = tab.moduleId;
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.tabName = tab.name;
    if (tab?.name == "IC") {
      this.isLtm = false;
      this.isYtd = false;
    }
    this.setPeriodsOptions(this.subSectionFields);
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      let tabList = x.body?.financialTypesModelList;
      let pageConfigTabs = this.subSectionFields;
      tabList = tabList?.filter((item: any) =>
        pageConfigTabs?.some((otherItem: any) =>
          otherItem.aliasName.includes(item.name)
        )
      );
      if (tabList != undefined && tabList.length > 0) {
        tabList = tabList.map((tab: any) => ({
          ...tab,
          moduleId: this.modelList.moduleId 
        }));
        this.tabValueTypeList = tabList;
        this.tabValueTypeList[0].active = true;
        this.tabName = this.tabValueTypeList[0].name;
        this.setPeriodsOptions(pageConfigTabs);
      }
    });
  }
  private setPeriodsOptions(pageConfigTabs: any[]) {
    let ltmYTDPeriodType = this.setLtmYtdPeriodType(this.tabName);
    let periodOptions = [
      { field: PeriodTypeFilterOptions.Monthly, key: false},
      { field: PeriodTypeFilterOptions.Quarterly, key: false },
      { field: PeriodTypeFilterOptions.Annual, key: false }
    ];
    let activeTabData = pageConfigTabs?.find(
      (x) => x.aliasName == ltmYTDPeriodType
    );
    if (this.isLtm || this.isYtd)
      this.valueTypeString = ltmYTDPeriodType;
    else
      this.valueTypeString = undefined;
    if (activeTabData == undefined) {
      activeTabData = this.processPageLoadView(activeTabData, ltmYTDPeriodType);
    }
    this.filterOptions = periodOptions?.filter(item =>
      activeTabData?.chartValue?.some(otherItem => otherItem === item.field));
    let periodType = this.filterOptions.find(x => x.key);
    if (periodType == undefined && this.filterOptions.length > 0) {
      for (const element of periodOptions) {
        element.key = false;
      }
      this.filterOptions[0].key = true;
      periodType = this.filterOptions[0];
    }
    this.onChangePeriodOption(periodType);
  }



  /**
   * Handles the audit log functionality for a specific row and field.
   * @param rowData - The data of the row.
   * @param field - The field associated with the row.
   */
  onAuditLog(rowData: any, field: any) {
    sessionStorage.removeItem(CreditKpiConstants.CreditKpiAuditLocalStorage);
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (!this.ErrorNotation || rowData.IsHeader) {
      if (this.ErrorNotation) {
        this.showErrorToast(ERROR_MESSAGE);
      }
      return;
    }
    if (this.isConvertedValue(rowData)) {
      const message = getConversionErrorMessage(
        this.kpiCurrencyFilterModel.isSpotRate,
        this.auditLogSpotRateConversionError,
        this.auditLogErrorForConvertedValue
      );
      this.toastrService.warning(message, '', { positionClass: ImpactKPIConstants.ToastCenterCenter });
      return;
    }
    const dateComponents = extractDateComponents(field.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
    this.auditService.getPortfolioEditSupportingCommentsData(auditLogFilter).subscribe({
      next: (data: MappedDocuments) => {
        if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
          let attributeName = rowData.KPI;
          this.redirectToAuditLogPage(field, attributeName, data, auditLogFilter);
        } else if (data?.auditLogCount == 0) {
          this.showErrorToast(GlobalConstants.AuditLogNAMessage);
        }
      },
      error: (error: any) => {
        this.showErrorToast(ERROR_MESSAGE);
      },
    });
  }
  /**
   * Returns an Audit object with the specified parameters.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components object containing year, month, and quarter.
   * @returns An Audit object with the specified parameters.
   */
  getAuditLogFilter(rowData: any, dateComponents: { year: any; month: number; quarter: any; }) {
    return <Audit>{
      valueType:
        this.valueTypeString != undefined
          ? this.valueTypeString
          : this.tabValueTypeList.length == 0
            ? CreditKpiConstants.Actual
            : this.tabName,
      kpiId: rowData["KpiId"],
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: this.modelList.moduleId,
      companyId: this.modelList.portfolioCompanyID,
    };
  }


  /**
   * Redirects to the audit log page with the specified parameters.
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents data.
   * @param auditLogFilter - The audit log filter.
   */
  redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
    let routeData = {
      KPI: "Credit KPI Beta",
      header: field.header,
      PortfolioCompanyID: this.modelList.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId: this.modelList.moduleId,
      Comments: this.valueTypeString != undefined
        ? this.valueTypeString
        : this.tabValueTypeList.length == 0
          ? CreditKpiConstants.Actual
          : this.tabName,
      currency: this.modelList?.reportingCurrencyDetail?.currencyCode,
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
    };

    // Store the data in local storage or session storage
    sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.CREDIT_KPI);
    sessionStorage.setItem(CreditKpiConstants.CreditKpiAuditLocalStorage, JSON.stringify(routeData));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + CreditKpiConstants.CreditKpiAuditUrl;
      window.open(myAppUrl, '_blank');
    }
  }
  showErrorToast(message: string, title: string = '', position: string = ImpactKPIConstants.ToastCenterCenter): void {
    this.toastrService.error(message, title, { positionClass: position });
  }
  /**
   * Handles the initialization of the edit process for a given row and column.
   * 
   * @param rowData - The data of the row that is being edited.
   * @param column - The column that is being edited.
   * 
   * This method performs several checks before allowing the edit process to proceed:
   * - Checks if the user has permission to edit.
   * - Displays an error toast if the user does not have access.
   * - Warns the user if the value is a converted value.
   * - Checks if the user has permission to edit the specific row data.
   * - Updates information if necessary.
   * - Shows an upload popup if required.
   * - Displays an error toast if none of the conditions are met.
   */
  onEditInit(rowData: any, column: any) {
    const canEdit = this.canEdit();
    if (!canEdit && (this.modelList?.moduleId < 3|| this.modelList?.moduleId >= 17 )) {
      this.showErrorToast(ErrorMessage.NoAccess);
      return;
    }
    if (this.ErrorNotation) {
      return;
    }

    if (this.isConvertedValue(rowData)) {
             const message = getConversionErrorMessage(
         this.kpiCurrencyFilterModel.isSpotRate,
         this.editSpotRateConversionError,
         this.editErrorForConvertedValue
       );
      this.toastrService.warning(message, '', { positionClass: ImpactKPIConstants.ToastCenterCenter });
      return;
    }

    if (!this.hasPermissionToEdit(rowData) && this.modelList?.moduleId < 3) {
      return;
    }

    if (this.shouldUpdateInfo(rowData)) {
      this.infoUpdate = true;
    } else if (this.shouldShowUploadPopup(rowData, column)) {
      this.showUploadPopup(rowData, column);
    } else {
      this.showErrorToast(GlobalConstants.CellEditError);
    }
  }

  /**
   * Determines if the current user has edit permissions based on the module type.
   *
   * @returns {boolean} - Returns `true` if the user has edit permissions for the current module, otherwise `false`.
   */
  private canEdit(): boolean {
    switch (this.modelList?.moduleId) {
      case KPIModulesEnum.CreditKPI:
        return this.creditKPIPermissions?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.TradingRecords:
        return this.tradingRecordPermissions?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI1:
        return this.otherKPI1Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI2:
        return this.otherKPI2Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI3:
        return this.otherKPI3Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI4:
        return this.otherKPI4Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI5:
        return this.otherKPI5Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI6:
        return this.otherKPI6Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI7:
        return this.otherKPI7Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI8:
        return this.otherKPI8Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI9:
        return this.otherKPI9Permission?.map(access => access.canEdit).includes(true);
      case KPIModulesEnum.OtherKPI10:
        return this.otherKPI10Permission?.map(access => access.canEdit).includes(true);
      default:
        return false;
    }
  }

  /**
   * Checks if there is an error notation.
   *
   * @returns {boolean} - Returns `true` if there is an error notation, otherwise `false`.
   */
  private isConvertedValue(rowData: any): boolean {
    return this.isValueConverted && rowData['KPI Info'] === '$';
  }

  /**
   * Checks if the user has permission to edit the given row data.
   *
   * This method verifies if the user has the necessary permissions to edit
   * the Investment KPIs sub-feature and ensures that the row data is not
   * marked as a formula.
   *
   * @param rowData - The data of the row to check for edit permissions.
   * @returns `true` if the user has permission to edit and the row data is not a formula, otherwise `false`.
   */
  private hasPermissionToEdit(rowData: any): boolean {
    return !(
      (this.kpiName === "TradingRecords" && !this.permissionService.checkUserPermission(this.subFeature.TradingRecords, ActionsEnum[ActionsEnum.canEdit], this.id)) ||
      (this.kpiName === "CreditKpi" && !this.permissionService.checkUserPermission(this.subFeature.CreditKPI, ActionsEnum[ActionsEnum.canEdit], this.id)) ||
      rowData.isHeader
    );
  }

  /**
   * Determines whether the information should be updated based on the provided row data.
   *
   * @param rowData - The data of the row to check.
   * @returns `true` if the information should be updated; otherwise, `false`.
   */
  private shouldUpdateInfo(rowData: any): boolean {
    return Number(this.masterKpiValueUnit.typeId) !== FinancialValueUnitsEnum.Absolute &&
      !this.ErrorNotation &&
      !rowData.IsHeader;
  }

  /**
   * Determines whether the upload popup should be shown based on the provided row data and column.
   *
   * @param rowData - The data of the row being evaluated.
   * @param column - The column information (not used in the current implementation).
   * @returns A boolean indicating whether the upload popup should be shown.
   */
  private shouldShowUploadPopup(rowData: any, column: any): boolean {
    return !rowData.IsHeader;
  }

  /**
   * Displays the upload popup with the provided row data and column information.
   *
   * @param rowData - The data for the selected row.
   * @param column - The column information for the selected row.
   * @private
   */
  private showUploadPopup(rowData: any, column: any): void {
    this.uniqueModuleCompany = {
      moduleId: this.modelList?.moduleId,
      companyId: this.modelList.portfolioCompanyID,
      valueType: this.setLtmYtdPeriodType(this.tabName),
    };
    this.dataRow = rowData;
    this.dataColumns = column;
    this.isUploadPopupVisible = true;
  }

  CloseInfo() {
    this.infoUpdate = false;
  }

  getValues(rowdata: any, column: any) {
    let result = this.getFilterAuditValue(rowdata, column);
    if (result.length > 0) {
      return result[0].pcCompanyKPIMonthlyValueID;
    }
    else
      return 0;
  }

  getFilterAuditValue(rowdata: any, column: any) {
    let headers = column.field.split(' ');
    let auditList = this.auditLogList;
    let periodHeader = null;
    let yearHeader = null;
    let monthValue = null;
    if (headers?.length > 0 && auditList?.length > 0) {
      if (headers.length == 1)
        yearHeader = parseInt(headers[0]);
      else {
        periodHeader = headers[0];
        yearHeader = parseInt(headers[1]);
        if (!periodHeader.toLowerCase().includes("q"))
          monthValue = this.miscService.getMonthNumber(periodHeader);
      }
    }
    return this.filterAuditValue(yearHeader, monthValue, auditList, periodHeader, rowdata);
  }
  filterAuditValue(yearHeader: any, monthValue: any, auditList: any, periodHeader: any, rowdata: any) {
    let result = [];
    if (periodHeader == "Q1" || periodHeader == "Q2" || periodHeader == "Q3" || periodHeader == "Q4")
      result = auditList.filter(x => x.quarter == periodHeader && x.year == yearHeader && x.companyKPIID == rowdata.ValuesKpiId);
    else if (monthValue != null)
      result = auditList.filter(x => x.month == monthValue && x.year == yearHeader && x.companyKPIID == rowdata.ValuesKpiId);
    else
      result = auditList.filter(x => x.month == null && x.year == yearHeader && (x.Quarter == null || x.Quarter == '') && x.companyKPIID == rowdata.ValuesKpiId);
    return result
  }

  onColumnEditComplete(index: any, col: any, rowData: any, event) {
    let prevVal = this.updateModel.previousVal;
    rowData[col.field] = event != undefined ? event.target.value ?? undefined : rowData[col.field];
    rowData[col.field] = rowData[col.field] == "" ? undefined : rowData[col.field];
    let currVal = rowData[col.field];
    if (!this.confirmUpdate && currVal != prevVal) {
      this.updateModel.updatedVal = rowData[col.field];
      this.confirmUpdate = true;
    }
    else
      this.OnKpiUpdateCancel("");
  }
  getTabName() {
    switch (this.tabName) {
      case FinancialsSubTabs.Actual:
        return "actual";
      case FinancialsSubTabs.Budget:
        return "budget";
      case FinancialsSubTabs.Forecast:
        return "forecast";
      case FinancialsSubTabs.IC:
        return "ic";
    }
  }
  

  successToaster() {
    this.toastrService.success("Entry updated successfully", "", { positionClass: "toast-center-center" });
  }

  OnKpiUpdateCancel(event: any) {
    let objIndex = this.tableResult.findIndex((obj => obj.KpiId == this.updateModel.kpiId));
    this.tableResult[objIndex][this.updateModel.colName] = this.updateModel.previousVal;
    this.clearcellEdit();
  }

  onColumnEdit(event: any) {
    event.target.blur();
  }
  clearcellEdit() {
    let objIndex = this.tableResult.findIndex((obj => obj.KpiId == this.updateModel.kpiId));
    this.tableResult[objIndex][`${this.updateModel.colName} editable`] = false;
    this.confirmUpdate = false;
    this.updateModel = {};
  }
  validateNumber(event: any, KpiInfo: string) {
    if (event.which != 15) {
      let ex: RegExp = new RegExp(/^-*\d*(?:[.,]\d{1,6})?$/);
      if (ex.test(event.target.value) === false) {
        if (event != undefined && (event.target as HTMLInputElement).value.includes(".")) {
          event.target.value = parseFloat(event.target.value).toFixed(6);
        }
      }
    }
  }
  validateMaxLength(event: any): boolean {
    if (event != null && (event.target as HTMLInputElement).value.includes(".")) {
      if (event.target.value.length == 21) return false;
    } else {
      if (event.target.value.length == 16) return false;
    }
    return true;
  }
  exportMasterKpiValues() {
    let canExport = false;
    switch (this.modelList?.moduleId) {
      case KPIModulesEnum.CreditKPI:
        canExport = this.creditKPIPermissions?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.TradingRecords:
        canExport = this.tradingRecordPermissions?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI1:
        canExport = this.otherKPI1Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI2:
        canExport = this.otherKPI2Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI3:
        canExport = this.otherKPI3Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI4:
        canExport = this.otherKPI4Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI5:
        canExport = this.otherKPI5Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI6:
        canExport = this.otherKPI6Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI7:
        canExport = this.otherKPI7Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI8:
        canExport = this.otherKPI8Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI9:
        canExport = this.otherKPI9Permission?.map(access => access.canExport).includes(true);
        break;
      case KPIModulesEnum.OtherKPI10:
        canExport = this.otherKPI10Permission?.map(access => access.canExport).includes(true);
        break;
    }
    if (canExport || (this.modelList?.moduleId > 2 && this.modelList?.moduleId < 17)) {
      let event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "FinancialKPI.KPI",
        multiSortMeta: this.financialKPIMultiSortMeta,
        sortOrder: -1,
      };
      let filter = {
        currency: this.modelList?.reportingCurrencyDetail?.currency,
        decimaPlace: this.modelMasterKpi?.decimalPlaces?.type,
        valueType: this.masterKpiValueUnit?.typeId,
      };
      this.exportMasterKPILoading = true;
      this.portfolioCompanyService
        .exportTradingRecordsList({
          companyId: this.modelList?.portfolioCompanyID?.toString(),
          portfolioCompanyID: this.modelList?.portfolioCompanyID?.toString(),
          paginationFilter: event,
          searchFilter: this.financialKpiSearchFilter,
          kPIFilter: filter,
          moduleId: this.modelList?.moduleId,
          Unit: this.masterKpiValueUnit.typeId,
          IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
          SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
          currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
          SpotRate:this.kpiCurrencyFilterModel.isSpotRate ? this.kpiCurrencyFilterModel.spotRate : null,
          reportingCurrencyCode: this.modelList?.reportingCurrencyDetail?.currencyCode,
          currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
        })
        .subscribe(
          (response) => {
            this.exportMasterKPILoading = false;
            this.miscService.downloadExcelFile(response);
          },
          (error) => {
            this.exportMasterKPILoading = false;
            this.message = this.miscService.showAlertMessages(
              "error",
              ErrorMessage.SomethingWentWrong
            );
          }
        );
    }
    else {
      this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
    }
  }
  handleChange(e) {
    this.ErrorNotation = e;
  }
  printColumn(rowData: any, column: any) {
    let result = this.getFilterAuditValue(rowData, column);
    if (result.length > 0)
      return result[0].acutalAuditLog ? result[0].acutalAuditLog : false;
    else
      return false;
  }
  onChangeValueTypeOption(type) {
    if (this.tabName != FinancialsValueTypes.IC) {
      this.valueTypeString = this.tabName + " " + type;
      let isPageLoad = this.setPageLoad();
      if (isPageLoad) {
        this.isMonthly = true;
        this.isAnnually = false;
        this.isQuarterly = false;
        this.setDefaultTypeTab();
      }
      this.setLtmAndYtdFlags(type);
      if (!this.isLtm && !this.isYtd) {
        this.valueTypeString = undefined;
      }
      this.setPeriodsOptions(this.subSectionFields);
    }
  }
  setLtmAndYtdFlags(type: string) {
    if (type == FinancialsValueTypes.YTD) {
      this.isYtd = !this.isYtd;
      this.isLtm = false;
    } else {
      this.isLtm = !this.isLtm;
      this.isYtd = false;
    }
  }
  setPageLoad() {
    let isPagLoad = false;
    if (this.isLtm && this.isLtmPageLoad) {
      isPagLoad = true;
      this.isLtmPageLoad = false;
    } else if (this.isYtd && this.isYtdPageLoad) {
      isPagLoad = true;
      this.isYtdPageLoad = false;
    }
    return isPagLoad;
  }
  shouldAddLtm(periodType: string) {
    return (
      this.isLtm &&
      this.hasLtm &&
      !periodType.includes(FinancialsValueTypes.LTM)
    );
  }
  shouldAddYtd(periodType: string) {
    return (
      this.isYtd &&
      this.hasYtd &&
      !periodType.includes(FinancialsValueTypes.YTD)
    );
  }
  resetPeriodFlags() {
    this.isLtm = false;
    this.isYtd = false;
  }
  getPeriodTypeWithSuffix(periodType: string) {
    if (this.shouldAddLtm(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.LTM}`;
    } else if (this.shouldAddYtd(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.YTD}`;
    } else {
      this.resetPeriodFlags();
    }
    return periodType;
  }
  setLtmYtdPeriodType(periodType: string) {
    const ltmType = `${periodType} ${FinancialsValueTypes.LTM}`;
    const ytdType = `${periodType} ${FinancialsValueTypes.YTD}`;
    this.hasLtm = this.checkExistence(ltmType);
    this.hasYtd = this.checkExistence(ytdType);
    if (this.hasLtm || this.hasYtd)
      periodType = this.getPeriodTypeWithSuffix(periodType);
    return periodType;
  }
  checkExistence(type: string) {
    const res = this.pageConfigResponse?.kpiConfigurationData?.filter(
      (x) => x.aliasName === type
    );
    return res?.length > 0;
  }
  updatePeriodTypeFlags() {
    if (this.hasYtd && !this.isYtd && (!this.hasLtm || !this.isLtm)) {
      this.isYtd = true;
      this.isLtm = false;
    } else if (this.hasLtm && !this.isLtm && (!this.hasYtd || !this.isYtd)) {
      this.isLtm = true;
      this.isYtd = false;
    }
  }
  processPageLoadView(
    selectedPeriodTypeConfiguration: any,
    periodType: string
  ) {
    if (selectedPeriodTypeConfiguration == undefined) {
      this.updatePeriodTypeFlags();
      periodType = this.getPeriodTypeWithSuffix(periodType);
      selectedPeriodTypeConfiguration =
        this.findPeriodTypeConfiguration(periodType);
    }
    this.valueTypeString = periodType;
    return selectedPeriodTypeConfiguration;
  }
  findPeriodTypeConfiguration(periodType: string) {
    return this.pageConfigResponse?.kpiConfigurationData.find(
      (x) => x.aliasName === periodType
    );
  }
  //#endregion New Code

  /**
   * Handles the submit button event.
   * @param results - The results object containing the code and message.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == ImpactKPIConstants.ok) {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
      this.isValueUpdated = !this.isValueUpdated;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  /**
   * Displays a success toast notification.
   * 
   * @param message - The message to be displayed in the toast.
   * @param title - The title of the toast (optional).
   * @param position - The position of the toast on the screen (optional, default: "toast-center-center").
   */
  showSuccessToast(
    message: string,
    title: string = "",
    position: string = ImpactKPIConstants.ToastCenterCenter
  ): void {
    this.toastrService.success(message, title, { positionClass: position });
  }
  /**
   * Handles the cancel button event.
   */
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }
}
function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe(
    data => to.next(data),
    err => to.error(err),
    () => to.complete(),
  );
}
