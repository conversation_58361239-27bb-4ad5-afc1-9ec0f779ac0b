<div class="row bborder header-box_shadow p-3 header-page pt-0">
    <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 col-xs-12 pr-0 pl-0">
        <div class="float-left">
            <kendo-combobox id="page-dropdown" [clearButton]="false"  [(ngModel)]="selectedPageItem" 
                [fillMode]="'solid'" [filterable]="false" name="displayName" 
                class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="pageDropdownOptions"
                [filterable]="true"  [valuePrimitive]="false" textField="displayName" placeholder="Select an option"
                (valueChange)="onPageDdSelectionChange($event);" valueField="id">
            </kendo-combobox>
        </div>
        <div class="float-right">
            <button id="page-config-reset" type="reset" title="Clear" class="width-120 nep-button nep-button-secondary" (click)="reset()"
                [disabled]="!f.form.valid || isDisabledBtn">Reset</button>
            <button id="page-config-save-changes" type="submit" title="Save" class=" width-120 nep-button nep-button-primary width-135"
                [disabled]="!f.form.valid|| isDisabledBtn" (click)="loadPopup()">Save changes
            </button>
        </div>
    </div>
</div>
<div class="static-info-modification">
    <app-static-info-modification-message [portfolioCompanySelected]="isPortfolioCompanySelected" class="config-note"></app-static-info-modification-message>
</div>
<form name="form" (ngSubmit)="f.form.valid && loadPopup()" [mustMatch]="existingTags" #f="ngForm">
    <div class="drag-boundary" cdkScrollable cdkDropListAutoScroll>
        <div class="" [cdkDropListData]="subPageList" cdkDropList #toWatch="cdkDropList" (cdkDropListDropped)="dropSection($event)">
    <div cdkDragBoundary=".drag-boundary" cdkDragLockAxis="y" cdkDrag #elem="cdkDrag" [cdkDragDisabled]="(selectedPageItem?.name != 'Portfolio Company' || page.name==pageConfigurationDocumentPageDetails.Documents) && !selectedPageItem?.isDragDrop" class="section-container page-section row mr-0 ml-0 mt-3" *ngFor="let page of subPageList;index as i;">
        <div class="col-12 pr-0 pl-0">
            <div class="row page-section p-2 mr-0 ml-0 section-header">
                <div class="col-10 pr-0 pl-0">
                    <div class="row mr-0 ml-0">
                        <div class="subpage-name-col col-4 pt-2 pr-0 pl-0 pb-0">
                            <div class="dot-img" cdkDragHandle>
                                <img src="assets/dist/images/6dots.svg" alt="">
                            </div>
                            <div class="displayName-topCard">
                                {{page.name}}
                            </div>
                        </div>
                        <div class="col-5 pt-0 pb-0">
                            <input type="text" class="form-control field-text eachlabel-padding default-txt"
                                name="{{page.displayName}}{{i}}" value="{{page.displayName}}"
                                #{{page.displayName}}="ngModel" [(ngModel)]="page.displayName" required
                                (keyup)="checkAnyDataChange()" autocomplete="off" maxlength="100"
                                placeholder="Click here to enter field name" />
                        </div>
                        <div [ngClass]="page.isDynamicFieldSupported ?'col-3 pt-1' : 'col-0 pt-2'">
                            <nep-button Type="Secondary" (click)="addCustomField(page);" Name="page-config-{{page.isDataType!=0?'Column':'Field'}}-{{page.name}}"
                                *ngIf="page.isDynamicFieldSupported" class="field-nep-button mr-2">
                                <img src="assets/dist/images/plus-icon.svg" class="cursor-filter" alt="" class="plus" />
                                {{page.isDataType!=0?'Column':'Field'}}
                            </nep-button>
                            <span
                                [ngClass]="page.isDynamicFieldSupported ? 'gbp-info col-sm-2 p-0  inline-block' :'gbp-info p-0 col-sm-1 inline-block' "
                                tooltipPosition="top"
                                [pTooltip]="page.name == 'Company Performance' ? 'You can hide & change order of default fields.' : (page.isDynamicFieldSupported ? 'You can hide & edit names of default fields. Custom fields can also be deleted' : 'You can hide & edit names of default fields.')"
                                tooltipStyleClass="bg-grey-color"><img class="FCinfoIconImg-24 cursor-filter"
                                    [src]="'assets/dist/images/InfoGrey.svg'" alt="" />
                            </span>
                        </div>

                    </div>
                </div>
                
                <div class="col-2 pt-2 pb-2 pr-0">
                    
                    <div class="float-right">
                        <span class="pr-2"  *ngIf="page.parentId==pageConfigurationPageDetails.ESG || page.name==pageConfigurationDocumentPageDetails.Documents"> Hide
                        <kendo-switch  name="{{page.name}}+1" [(ngModel)]="!page.isActive" (valueChange)="onMainCardToggleChange($event,page.isTabExpanded,i)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                       </span>
                       <span class="pr-2"  *ngIf="page.name == 'Commentary'"> Footnotes
                        <kendo-switch  name="{{page.name}}Footnotes+1" [(ngModel)]="page.isFootNote" (valueChange)="onCommentaryFootnoteToggleChange($event,page.isTabExpanded,i)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                       </span>
                        <span class="pr-2 d-none" *ngIf="page.name=='Static Information'">
                            <img src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" />
                        </span>
                        <span class="pl-2" id="page-config-expand-card">
                            <span class="expand-doc cursor-filter" (click)="onTabToggle(page);"><span class="pr-2 ">
                                    <i [ngClass]="page.isTabExpanded?'pi pi-chevron-up':'pi pi-chevron-down'"
                                        aria-hidden="true"></i>
                                </span></span>
                        </span>
                        <div>
                           
                    </div>

                    </div>
                </div>

            </div>
            <div class="section-content" *ngIf="page.isTabExpanded&&page?.subPageFieldList?.length">
                <div *ngIf="page?.subPageFieldList.length>0" cdkDropList #toWatch="cdkDropList"
                    [cdkDropListData]="page?.subPageFieldList" (cdkDropListDropped)="page.isDragDrop?drop($event):null">
                    <div class="row page-section p-2 m-3" #sectioncontent
                        *ngFor="let field of page?.subPageFieldList | pageSettingIsDelete:filterDelete;index as j"
                        cdkDrag #elem="cdkDrag" [cdkDragDisabled]="field?.name === commentaryPeriod">
                        <div class="col-12 pr-0 pl-0">
                            <div class="row mr-0 ml-0">
                                <div class="col-8 pr-0 pl-0">
                                    <div class="row mr-0 ml-0">
                                        <div class=" col-3 pt-2 pb-2 pr-0 pl-0 pb-0"
                                            [ngClass]="page.isDragDrop?'':'pl-isdropdown-false'">
                                            <span class="pr-2" [ngStyle]="{'cursor': 'pointer' }"
                                                *ngIf="page.isDragDrop">
                                                <img class="dot-img-SubCard" src="assets/dist/images/6dots.svg" alt="">
                                            </span>
                                            <span class="pt-1">
                                                <label title="{{field.name}}" class="pt-0 mb-0 displayName">
                                                    {{field.name}}
                                                </label>
                                            </span>

                                        </div>
                                        <div class="field-name-col col-4 pt-0 pb-0" *ngIf="field.name !== commentaryPeriod && field.name !== images">
                                            <input title="{{field.displayName}}" type="text"
                                                class="form-control field-text eachlabel-padding default-txt p-0"
                                                required="true" [tagValidator]="page.isTabExpanded?existingTags:[]"
                                                [(ngModel)]="field.displayName" value="{{field.displayName}}"
                                                name="itemInput--{{field.id}}{{j}}" #itemInput="ngModel"
                                                autocomplete="off" placeholder="Click here to enter field name"
                                                maxlength="100" (keyup)="onKeyup(page);checkAnyDataChange();"
                                                (mousedown)="$event.stopPropagation()" (change)="onChangeSubField(page, field)" (input)="onInput(page, field)"  [disabled]="!field.isActive" />
                                        </div>
                                        <div class="col-0 pt-2"
                                            *ngIf="(field.displayName == null || field.displayName == '' || field.displayName === undefined) || (itemInput?.errors != null && itemInput?.errors?.mustMatch) || field?.isInValid">
                                            <span tooltipPosition="top" 
                                                [pTooltip]="(field.displayName == null || field.displayName == '' || field.displayName === undefined) ? pageConfigeFieldRequired : pageConfigeDuplicateNameWarning"
                                                tooltipStyleClass="bg-grey-color">
                                                <img class="FCinfoIconImg-24 cursor-filter"
                                                    [src]="'assets/dist/images/PageInfo.svg'" alt="" />
                                            </span>
                                        </div>
                                        <div class="col-3 pt-0 pb-0" *ngIf="page.isDataType&&field.dataTypeId!=0">
                                            <kendo-combobox [disabled]="page.isDataType&&(pageConfigurationsDatalist|arrayFilterTrueOrFalse:field)" [clearButton]="false"  [(ngModel)]="field.dataTypeId"
                                                #dataType="ngModel" [fillMode]="'flat'" [filterable]="true" name="dataType-{{j}}" 
                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35 typeheader-margin" [size]="'medium'"
                                                [data]="(field.subPageID==1||field.parentId==1)?pcDataTypes:trackRecordDataTypes" [valuePrimitive]="true" [filterable]="false" textField="dataType" valueField="trackRecordId"
                                                [placeholder]="'Select Data Type'" [Required]="true">
                                            </kendo-combobox>
                                        </div>
                                        <div class="col-0 pt-2"
                                            *ngIf="page.isDataType&&(pageConfigurationsDatalist|arrayFilterTrueOrFalse:field)">
                                            <span tooltipPosition="top"
                                                [pTooltip]="'Data type cannot be changed after uploading data for this custom field. You can delete this custom field and create a new one. Note: Deleting custom field will also delete the uploaded data for it.'"
                                                tooltipStyleClass="bg-grey-color"><img
                                                    class="FCinfoIconImg-24 cursor-filter"
                                                    [src]="'assets/dist/images/InfoGrey.svg'" alt="" />
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4 pt-0 pb-0">
                                    <div class="float-right  float-rght-width custom-right-sec"
                                        *ngIf="!field.isCustom && !field.isMandatory && field?.name !== commentaryPeriod && page.name!=pageConfigurationDocumentPageDetails.Documents">
                                        <span class="pr-2 Caption-M"> Hide
                                        </span>
                                        <kendo-switch  id="hide-switch-{{page.name}}" name="{{field.name}}+1" [disabled]="page.parentId==pageConfigurationPageDetails.ESG ?!page.isActive:null" [(ngModel)]="!field.isActive" (valueChange)="onToggleChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right  float-rght-width custom-right-sec"
                                        *ngIf="field.isCustom && page.parentId==pageConfigurationPageDetails.ESG && !field.isMandatory">
                                        <span class="pr-2 Caption-M"> Hide
                                        </span>
                                        <kendo-switch  name="{{field.name}}+1" [disabled]="!page.isActive" [(ngModel)]="!field.isActive" (valueChange)="onToggleChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right mandatory pr-2" *ngIf="field.isMandatory">
                                        (Mandatory)
                                    </div>

                                    <div class="float-right float-rght-width" id="delete-custom-field-{{page.name}}"
                                        [ngClass]="page.parentId==pageConfigurationPageDetails.ESG ? 'pr-4' : ''"
                                        *ngIf="field.isCustom">
                                        <a class="btn icon-24 p-0 butn-fr" title="Delete"
                                            (click)="removeCustomField(page, field)" id="delete-custom-field-{{page.name}}" >
                                            <img src="assets/dist/images/Trash-icon.svg" class="cursor-filter" id="delete-custom-field-{{page.name}}" alt="" />
                                        </a>
                                    </div>

                                    <div class="float-right  pr-2 custom-right-sec"
                                        *ngIf="field.name !='FundName' && field.showOnList">
                                        <span class="pr-2 Caption-M"> List page
                                        </span>
                                        <kendo-switch id="list-page-switch-{{page.name}}" [ngClass]="disablePageList && !field.isListData ? 'disable' : ''"  name="{{field.id}}" [disabled]="disablePageList && !field.isListData"
                                        [(ngModel)]="field.isListData" (valueChange)="onListToggleChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right  pr-2 custom-right-sec"
                                        *ngIf="field.name !='CompanyLogo' && selectedPageItem.name == 'Portfolio Company' && page.name == 'Static Information'">
                                        <span class="pr-2 Caption-M"> HighLight
                                        </span>
                                        <kendo-switch id="highlight-page-highlight-{{page.name}}"   name="{{field.id}}+1"
                                        [(ngModel)]="field.isHighLight" [disabled]="false" (valueChange)="onHighLightChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right  pr-2 custom-right-sec"
                                        *ngIf="selectedPageItem.name == 'Deals' && page.name == 'Portfolio Company Fund Holding Details'">
                                        <span class="pr-2 Caption-M"> Link to PC
                                        </span>
                                        <kendo-switch id="highlight-page-pc-link-{{page.name}}"   name="{{field.id}}+1"
                                        [(ngModel)]="field.isPcLink" [disabled]="false" (valueChange)="onPcLinkChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right  pr-2 custom-right-sec"
                                        *ngIf="field.subPageID==kpiSubPageId||field.subPageID==financial || field.subPageID == cabTabSubPageId || field.subPageID == otherKpi || field.subPageID == valuationSummaryId || field.subPageID == otherCabTabSubPageId">
                                        <span class="pr-2 Caption-M"> Chart
                                        </span>
                                        <kendo-switch  id="chart-switch-{{page.name}}" name="Chart{{j}}" 
                                        [(ngModel)]="!field.isChart" (valueChange)="onToggleChartChange($event,j,i,page.isTabExpanded)"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>
                                    <div class="float-right  pr-2 custom-right-sec"
                                        *ngIf="field.subPageID==kpiSubPageId||field.subPageID==financial || field.subPageID == cabTabSubPageId || field.subPageID == otherKpi || field.subPageID == valuationSummaryId || field.subPageID == otherCabTabSubPageId">
                                        <span class="pr-2 Caption-M">Trend
                                        </span>
                                        <kendo-switch id="name-trend-switch-{{page.name}}" name="Trend{{j}}"
                                        [(ngModel)]="!field.isTrend" (valueChange)="onToggleTrendChange($event,j,i,page.isTabExpanded)" [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-12 pr-0 pl-0" *ngIf="field?.mSubFields?.length>0">
                            <div class="row mr-0 ml-0">
                                <div class="col-12 pr-0 pl-0 pt-4 pb-2">
                                    <ng-container *ngFor="let subField of field?.mSubFields | orderBy:'sectionID'; let subIndex = index">
                                        <div class="value-type-section d-inline-block">
                                            <div class="label-ctrl">
                                                <span *ngIf="page.name=='CapTable' || page.name=='OtherCapTable' || field.name === commentaryPeriod">{{subField.aliasName}}</span>
                                                <div *ngIf="page.name!='CapTable' && page.name!='OtherCapTable' && field.name !== commentaryPeriod">
                                                    <ng-container *ngIf="isStatusIncluded(subField?.name); else other">
                                                        <div class="TextTruncate" title="{{subField.aliasName}}">
                                                            {{subField.aliasName}}
                                                        </div>
                                                    </ng-container>
                                                    <ng-template #other>
                                                        <div class="type-text">
                                                            <div class="TextTruncate" title="{{subField.aliasName}}" *ngIf="!editMode[subField.sectionID]">
                                                                {{subField.aliasName}}
                                                            </div>
                                                            <input *ngIf="editMode[subField.sectionID]" type="text"
                                                                class="form-control field-text eachlabel-padding default-txt p-0" required="true"
                                                                [(ngModel)]="subField.aliasName" name="{{subField.sectionID}}{{subIndex}}" #itemTextInput="ngModel"
                                                                autocomplete="off" placeholder="Enter field name" maxlength="30"
                                                                style="flex-grow: 1; margin-right: 10px;"
                                                                (input)="onKeyupSubPageSection(page,field,subField,subField.aliasName);"
                                                                (mousedown)="$event.stopPropagation()" (keypress)="onKeypress($event);"
                                                                (blur)="onOutsideClick(subField.sectionID,subField.aliasName);onKeyupSubPageSection(page,field,subField,subField.aliasName);" />
                                                            <img class="info pl6" alt=""
                                                                [src]="isValidInput[subField.sectionID]?'assets/dist/images/RedIcon.svg' : 'assets/dist/images/EditIconPageConfig.svg'"
                                                                (click)="onEditClick(subField.sectionID)" />
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </div>
                                            <div class="eachlabel-padding default-txt">
                                                <p-multiSelect class="pageConfig-multiSelect"
                                                    name="{{field.name}}{{subIndex}}" [showHeader]="false"
                                                    [options]="subField.options" [(ngModel)]="subField.chartValue"
                                                    placeholder="Select" [defaultLabel]="someText" optionLabel="name"
                                                    (onChange)="onChangeSubPageFields($event.checked,j,i,page.isTabExpanded)"
                                                    [selectedItemsLabel]="'{0} items selected'" [display]="comma">>
                                                    <ng-template let-value pTemplate="selectedItems">
                                                        <ng-template [ngIf]="value?.length ?? 0 > 0"
                                                            [ngIfElse]="defaultTemplate">
                                                            <div class="settingMoreThan15Chars">{{value}}</div>
                                                        </ng-template>
                                                        <ng-template #defaultTemplate>Select</ng-template>
                                                    </ng-template>
                                                    <ng-template let-options pTemplate="item">
                                                        <div>
                                                            <div>{{ options }}</div>
                                                        </div>
                                                    </ng-template>
                                                </p-multiSelect>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</form>
<div *ngIf="isPopup">
    <modal customwidth="489px" [modalTitle]="modalTitle" primaryButtonName="Confirm" secondaryButtonName="Cancel"
        (primaryButtonEvent)="OnConfig($event)" (secondaryButtonEvent)="OnCancel($event)"
        [disablePrimaryButton]="disableRenameConfirm">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-xs-12">
                You are going to make to changes. Confirm to proceed with changes.
            </div>
        </div>
    </modal>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>