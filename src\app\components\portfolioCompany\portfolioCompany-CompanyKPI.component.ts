import {Input, Component,  OnInit,  ViewChild, EventEmitter, AfterViewInit} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { NgxSpinnerService } from "ngx-spinner";
import { MenuItem } from "primeng/api";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { AccountService } from "../../services/account.service";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import {DecimalDigitEnum,ErrorMessage,ExportTypeEnum,FinancialValueUnitsEnum,MiscellaneousService,OrderTypesEnum,PeriodTypeQuarterEnum} from "../../services/miscellaneous.service";
import { ActionsEnum, FeaturesEnum, KPIModulesEnum, PermissionService, UserSubFeaturesEnum } from "../../services/permission.service";
import { PortfolioCompanyService } from "../../services/portfolioCompany.service";
import { AuditService } from "src/app/services/audit.service";
import { Observable, Subject, Subscription } from 'rxjs';
import { Table } from "primeng/table";
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { filter } from "rxjs/operators";
import { NumberDecimalConst, PeriodTypeFilterOptions, GlobalConstants, CellEditConstants, KpiTypesConstants, OperationalKPIConstants } from "src/app/common/constants";
import { isNil } from "src/app/utils/utils";
import { ITab } from "projects/ng-neptune/src/lib/Tab/tab.model";
import { FinancialsSubTabs } from "../../common/constants";
import { Audit, MappedDocuments, TableHeader } from '../file-uploads/kpi-cell-edit/kpiValueModel';
import { extractDateComponents } from "../file-uploads/kpi-cell-edit/cell-edit-utils";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { DatePipe } from "@angular/common";
import { getConversionErrorMessage } from "src/app/utils/utils";

@Component({
  selector: "portfolio-company-kpi",
  templateUrl: "./portfolioCompany-CompanyKPI.component.html",
  styleUrls: ["./portfolioCompany-CompanyKPI.component.scss"],
})
export class PortfolioCompanyKPIComponent implements OnInit,AfterViewInit {
  kpiModuleId=KPIModulesEnum.Company;
  @ViewChild('menu') uiuxMenu!: MatMenu;
  @ViewChild('companyMenuTrigger') menuTrigger: MatMenuTrigger;
  NumberDecimalConst = NumberDecimalConst; 
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  financialValueUnits: typeof FinancialValueUnitsEnum = FinancialValueUnitsEnum;
  exportType: typeof ExportTypeEnum = ExportTypeEnum;
  dataTable: any;
  message: any;
  searchFilter:any=null;
  @ViewChild('dt') dt: Table | undefined;
  private eventsSubscription: Subscription;
  @Input() events: Observable<void>;  id: any;
  frozenCols: any = [{ field: "KPI", header: "KPI" }];
  financialKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  companyKPIs: any[];
  @Input() model: any = {};
  @Input() companyKPIPermissions: any = [];
  items: MenuItem[];
  ddlModel: any = {
    companyKPIList: [],
    selectedCompanyKPI: "",
  };
  updateModel:any = {};
  msgTimeSpan: number;
  loading = false;
  confirmUpdate = false;
  companyKpiValueUnit: any;
  tableReload = false;
  exportCompanyKPILoading: boolean = false;
  CompanyKPIOrginalData: any[] = [];
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  ErrorNotation: boolean = false;
  infoUpdate: boolean = false;
  blankSpace: any = "&nbsp";
  isLoader: boolean = false;
  isToasterMessage = false;
  tableColumns = [];
  tableFrozenColumns = [];
  tableResult = [];
  tableResultClone = [];
  isToggleChecked:boolean=false;
  companyKpiFilterCols: any = [];
  auditLogList: any = [];
  isTaabo:boolean = false;
  getPCCompanyKPIValues: any;
  createCompanyKPILayOut: any;
  convertCompanyKPIValueUnits: any;
  isValueUpdated: boolean = false;
  tabValueTypeList: ITab[] = [];
  IsPageLoad: boolean = true;
  tabName: string = "";
  isMonthly: boolean = false;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  filterOptions: any[] = [];
  valueTypeIc: string = FinancialsSubTabs.IC;
  @Input() pageConfigData =[{kpiConfigurationData:[],hasChart:false,kpiType:""}];
  subSectionFields=[];
  filterOptionsCopy: any[] = [
    { field: "Monthly", key: this.isMonthly },
    { field: "Quarterly", key: this.isQuarterly },
    { field: "Annual", key: this.isAnnually }
  ];
  companyKpiConfigData={kpiConfigurationData:[],hasChart:false,kpiType:""};
  defaultType:string="Monthly";
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: any;
  dataRow: object;
  dataColumns: TableHeader;
  kpiCurrencyFilterModel: any = {};
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
  auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;

  constructor(
    private auditService: AuditService,
    private accountService: AccountService,
    private miscService: MiscellaneousService,
    private portfolioCompanyService: PortfolioCompanyService,
    private toastrService: ToastrService,
    private modalService: NgbModal,
    private _avRoute: ActivatedRoute,
    private spinner: NgxSpinnerService,
    private router: Router,
    private permissionService:PermissionService,
    private identityService: OidcAuthService,
    private datePipe: DatePipe
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }

    this.items = [];
    this.modelCompanyKpi.periodType = { type: PeriodTypeQuarterEnum.Last1Year };
    this.modelCompanyKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelCompanyKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-1",
    };
    this.companyKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }

  sourceURL: any;
  ngOnInit() {
    this.IsPageLoad = true;
    this.companyKpiConfigData = this.pageConfigData.find(x =>x.kpiType=="CompanyKPIs");
    this.subSectionFields = this.companyKpiConfigData?.kpiConfigurationData;
    this.getValueTypeTabList();
    this.toastrService.overlayContainer = this.toastContainer;
    this.sourceURL = this.miscService.GetPriviousPageUrl();
    this.getPortfolioCompanies(null);
    this.eventsSubscription = this.events?.subscribe((res) =>{
      this.searchFilter=res;
      this.getPortfolioCompanies(null);
    });
    this.msgTimeSpan = this.miscService.getMessageTimeSpan();
  }

  isNumberCheck(str: any) {
    return isNumeric(str);
  }

  getPortfolioCompanies(event:any) {
    this.isLoader = true;
    if (this.id != undefined) {
      this.loading = true;
      let searchFilter = this.searchFilter;
       if (event == null) {
      event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "CompanywiseKPI.KPI",
        multiSortMeta: this.companywiseKPIMultiSortMeta,
        sortOrder: -1,
      };
    }
    if (searchFilter == null) {
      let sortOrder =
        this.modelCompanyKpi.orderType.type == OrderTypesEnum.LatestOnRight
          ? [
              { field: "year", order: 1 },
              { field: "month", order: 1 },
            ]
          : [
              { field: "year", order: -1 },
              { field: "month", order: -1 },
            ];
      searchFilter = {
        sortOrder: sortOrder,
        periodType: this.modelCompanyKpi.periodType.type,
      };
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            this.modelCompanyKpi.startPeriod.getFullYear(),
            this.modelCompanyKpi.startPeriod.getMonth(),
            this.modelCompanyKpi.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            this.modelCompanyKpi.endPeriod.getFullYear(),
            this.modelCompanyKpi.endPeriod.getMonth(),
            this.modelCompanyKpi.endPeriod.getDate()
          )
        );
      }
    } else {
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            searchFilter.startPeriod.getFullYear(),
            searchFilter.startPeriod.getMonth(),
            searchFilter.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            searchFilter.endPeriod.getFullYear(),
            searchFilter.endPeriod.getMonth(),
            searchFilter.endPeriod.getDate()
          )
        );
      }
    }
    this.companyKpiSearchFilter = searchFilter;
    this.searchFilter = searchFilter;
      this.portfolioCompanyService
      .getCompanyKpiData({
        companyId: this.model.portfolioCompanyID.toString(),
        paginationFilter: event,
        searchFilter: this.searchFilter,
        valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: this.IsPageLoad,
        moduleId : KPIModulesEnum.Company,
        kpiConfigurationData:this.companyKpiConfigData.kpiConfigurationData,
        IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
        SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
        currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
        reportingCurrencyCode: this.model.reportingCurrencyDetail?.currencyCode,
        currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource
      })
        .subscribe(
          (result) => {
            this.loading = false;
            this.isToggleChecked=false;
            this.ErrorNotation=false;
            this.isLoader = false;
            this.tableReload = true;
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.auditLogList = result?.companyKpiAuditLog || [];
            this.tableResultClone = result?.rows || [];
            this.convertUnits()
            this.companyKpiFilterCols = [...this.tableFrozenColumns, ...this.tableColumns];
            this.IsPageLoad = false;
            if(result != null){
              this.isMonthly = result?.isMonthly; 
              this.isQuarterly = result?.isQuarterly;
              this.isAnnually  = result?.isAnnually;
              this.SetFilterOptionsKeys(result);
            }
          },
          (error) => {
            this.loading = false;
            this.isLoader = false;
            this.tableResult = [];
            this.tableResultClone = [];
            this.auditLogList = [];
            this.IsPageLoad = false;
          }
        );
    }
  }

  private SetFilterOptionsKeys(result: any) {
    this.filterOptions.forEach(element => {
      switch (element.field) {
        case "Monthly":
          element.key = result?.isMonthly;
          break;
        case "Quarterly":
          element.key = result?.isQuarterly;
          break;
        case "Annual":
          element.key = result?.isAnnually;
          break;
      }
    });
    this.setDefaultTypeTab();
  }

  convertUnits() {
    this.tableResult = [];
    let companyValueUnit = this.companyKpiValueUnit;
    let local = this;
    this.tableResultClone.forEach(function (
      value: any
    ) {
      const valueClone = JSON.parse(JSON.stringify(value));
      if (valueClone["KPI Info"] != "%" && valueClone["KPI Info"] != "x" && valueClone["KPI Info"] != "#" &&
        valueClone["KPI Info"] != "Text" && valueClone["KPI Info"] != "" 
      ) {
        switch (Number(companyValueUnit.typeId)) {
          case FinancialValueUnitsEnum.Absolute:
            break;
          case FinancialValueUnitsEnum.Thousands:
            local.tableColumns.forEach((col: any, index: any) => {
              if(valueClone[col.field] != 0){
                valueClone[col.field] =
                !isNil(valueClone[col.field])?!isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field])))
                    ? (valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field]) / 1000
                    : valueClone[col.field]: valueClone[col.field];
              }
            });
            break;
          case FinancialValueUnitsEnum.Millions:
            local.tableColumns.forEach((col: any, index: any) => { 
              if(valueClone[col.field] != 0){
                valueClone[col.field] =
                !isNil(valueClone[col.field])?!isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field]))) 
                    ? (valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field]) / 1000000
                    : valueClone[col.field]:valueClone[col.field];
              }
            });
            break;
          case FinancialValueUnitsEnum.Billions:
            local.tableColumns.forEach((col: any, index: any) => {
              if(valueClone[col.field] != 0){
                valueClone[col.field] =
                !isNil(valueClone[col.field])?!isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field])))
                    ? (valueClone[col.field].indexOf(',') > -1?valueClone[col.field].replace(/,/g, ''):valueClone[col.field]) / 1000000000
                    : valueClone[col.field]: valueClone[col.field];
              }
            });
            break;
        }
      }
      local.tableResult.push(valueClone)
    });
  }
  

  globalFilter: string = "";

  /***************Company KPI*************[Start]******************/

  objCompanyKPIList: any = [];
  companyKPICols: any = [];
  modelCompanyKpi: any = {};
  portfolioCompanyCompanyKPIValuesList: any[];
  portfolioCompanyCompanyKPIValuesListClone: any[];
  companyValueUnitTable: FinancialValueUnitsEnum =
    FinancialValueUnitsEnum.Millions;
  companyKpiSearchFilter: any;
  expandedCompanyKPIs: any[] = [];
  totalCompanyCompanyKPIValuesRecords: number;
  companyPeriodErrorMessage: string = "";

  companywiseKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];


  /***************Company KPI*************[End]******************/

  exportCompanyKpiValues() {
    const canExport = this.companyKPIPermissions?.map(access => access.canExport);
    if (canExport.includes(true)) {
      let event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "CompanywiseKPI.KPI",
        multiSortMeta: this.financialKPIMultiSortMeta,
        sortOrder: -1,
      };
      let filter = {
        currency: this.model.reportingCurrencyDetail?.currency,
        decimaPlace: this.modelCompanyKpi.decimalPlaces?.type,
        valueType: this.companyKpiValueUnit?.typeId,
      };
      this.exportCompanyKPILoading = true;
      this.portfolioCompanyService
        .exportCompanywiseKPIList({
          companyId: this.model.portfolioCompanyID?.toString(),
          portfolioCompanyID: this.model.portfolioCompanyID?.toString(),
          paginationFilter: event,
          searchFilter: this.companyKpiSearchFilter,
          kPIFilter: filter,
          moduleId: KPIModulesEnum.Company,
          Unit: this.companyKpiValueUnit.typeId,
          IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
          SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
          currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
          reportingCurrencyCode: this.model.reportingCurrencyDetail?.currencyCode,
          currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
          SpotRate:this.kpiCurrencyFilterModel.isSpotRate ? this.kpiCurrencyFilterModel.spotRate : null
        })
        .subscribe(
          (response) => {
            this.exportCompanyKPILoading = false;
            this.miscService.downloadExcelFile(response);
          },
          (error) => {
            this.exportCompanyKPILoading = false;
            this.message = this.miscService.showAlertMessages(
              "error",
              ErrorMessage.SomethingWentWrong
            );
          }
        );
    }
    else {
      this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
    }
  }

  portfolioInfoSectionModel: any = {};
  handleChange(e) {
    this.ErrorNotation = e;
  }

  /**
   * Prints the column value for a given row data and column.
   * @param rowData The data of the row.
   * @param column The column to retrieve the value from.
   * @returns The column value, or false if the value is not found.
   */
  printColumn(rowData: any, column: any) {
    let result = this.getFilterAuditValue(rowData,column);
      if (result.length > 0) {
        if(this.tabName.includes('Actual') || this.tabName.includes('Forecast'))
           return result[0].acutalAuditLog ? result[0].acutalAuditLog:false;
           else
           return result[0].budgetAuditLog ? result[0].budgetAuditLog:false;
      }
      else
        return false;
  }

  /**
   * Handles the audit log functionality for a specific row in the portfolio company component.
   * @param rowData - The data of the row.
   * @param field - The field information.
   */
  onAuditLog(rowData: any, field: any) {
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (this.ErrorNotation) {
      if (rowData.IsHeader || rowData.IsFormula || rowData.MasterFormula !== null) {
        this.showErrorToast(ERROR_MESSAGE);
        return;
      }
      if (this.isConvertedValue(rowData)) {
                 const message = getConversionErrorMessage(
           this.kpiCurrencyFilterModel.isSpotRate,
           this.auditLogSpotRateConversionError,
           this.auditLogErrorForConvertedValue
         );
        this.toastrService.warning(message, '', { positionClass: "toast-center-center" });
        return;
      }
      const dateComponents = extractDateComponents(field.header);
      let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
      this.auditService
        .getPortfolioEditSupportingCommentsData(auditLogFilter)
        .subscribe({
          next: (data: MappedDocuments) => {
            if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
              let attributeName = rowData.KPI;
              this.redirectToAuditLogPage(
                field,
                attributeName,
                data,
                auditLogFilter
              );
            } else if (data?.auditLogCount == 0) {
              this.showErrorToast(GlobalConstants.AuditLogNAMessage);
            }
          },
          error: (error: any) => {
            this.showErrorToast(ERROR_MESSAGE);
          },
        });
    }
    else {
      return;
    }
  }

  /**
   * Redirects to the audit log page with the specified parameters.
   *
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents data.
   * @param auditLogFilter - The audit log filter.
   */
  redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
    let params = {
      KPI: this.tabName,
      header: field.header,
      PortfolioCompanyID: this.model.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId: KPIModulesEnum.Company,
      Comments: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
      currency: '',
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
      MappingId: auditLogFilter.mappingId,
    };
    sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.COMPANY_KPI);
    sessionStorage.setItem(GlobalConstants.CompanyKpiAuditLocalStorage, JSON.stringify(params));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + OperationalKPIConstants.AuditLogRoute;
      window.open(myAppUrl, '_blank');
    }
  }

  /**
   * Returns an Audit object representing the filter criteria for the audit log.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components (year, month, quarter).
   * @returns An Audit object with the filter criteria.
   */
  getAuditLogFilter(
    rowData: any,
    dateComponents: { year: any; month: number; quarter: any }
  ) {
    return <Audit>{
      valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
      kpiId: rowData.KpiId,
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: KPIModulesEnum.Company,
      companyId: this.model.portfolioCompanyID,
    };
  }
  
  onColumnEdit(event: any) {
    event.target.blur();
  }

  /**
   * Initializes the edit operation for a row in the CompanyKPI component.
   * @param rowData - The data of the row being edited.
   * @param column - The column of the row being edited.
   */
  onEditInit(rowData: any, column: any) {
    if (!this.canEdit()) {
      this.showErrorToast(ErrorMessage.NoAccess);
      return;
    }
    if (this.ErrorNotation) {
      return;
    }

    if (!this.hasPermissionToEdit(column)) {
      return;
    }

    if (this.isConvertedValue(rowData)) {
             const message = getConversionErrorMessage(
         this.kpiCurrencyFilterModel.isSpotRate,
         this.editSpotRateConversionError,
         this.editErrorForConvertedValue
       );
      this.toastrService.warning(message, '', { positionClass: "toast-center-center" });
      return;
    }

    if (this.shouldUpdateInfo(rowData)) {
      this.infoUpdate = true;
    } else if (this.shouldShowUploadPopup(rowData)) {
      this.showUploadPopup(rowData, column);
    } else {
      this.showErrorToast(GlobalConstants.CellEditError);
    }
  }

  /**
   * Determines if the user has edit permissions.
   *
   * This method checks the `KPIPermissions` array to see if any of the permissions
   * include the ability to edit. It returns `true` if at least one permission allows editing,
   * otherwise it returns `false`.
   *
   * @returns {boolean} `true` if the user can edit, `false` otherwise.
   */
  private canEdit(): boolean {
    const canEdit = this.companyKPIPermissions?.map(access => access.canEdit);
    return canEdit.includes(true);
  }

  /**
   * Checks if the user has permission to edit a specific column.
   *
   * @param column - The column to check for edit permissions.
   * @returns `true` if the user has permission to edit the column and the column header is not "KPI", otherwise `false`.
   */
  private hasPermissionToEdit(column: any): boolean {
    return this.permissionService.checkUserPermission(
      this.subFeature.CompanyKPIs,
      ActionsEnum[ActionsEnum.canEdit],
      this.id
    ) && column.header !== "KPI";
  }

  /**
   * Checks if the value in the given row data is converted.
   *
   * @param rowData - The data of the row to check.
   * @returns `true` if the value is converted and the 'KPI Info' field is '$', otherwise `false`.
   */
  private isConvertedValue(rowData: any): boolean {
    return this.isValueConverted && rowData['KPI Info'] === '$';
  }

  /**
   * Determines whether the information should be updated based on the provided row data.
   *
   * @param rowData - The data of the row to be evaluated.
   * @returns `true` if the information should be updated; otherwise, `false`.
   */
  private shouldUpdateInfo(rowData: any): boolean {
    return Number(this.companyKpiValueUnit.typeId) != FinancialValueUnitsEnum.Absolute &&
      !this.ErrorNotation &&
      !rowData.IsHeader;
  }

  /**
   * Determines whether the upload popup should be shown based on the provided row data.
   *
   * @param rowData - The data of the row to evaluate.
   * @returns `true` if the upload popup should be shown; otherwise, `false`.
   */
  private shouldShowUploadPopup(rowData: any): boolean {
    return !rowData.IsHeader && !rowData.IsFormula && rowData.MasterFormula === null;
  }

  /**
   * Displays the upload popup with the provided row data and column information.
   *
   * @param rowData - The data for the selected row.
   * @param column - The column information related to the selected row.
   */
  private showUploadPopup(rowData: any, column: any): void {
    this.uniqueModuleCompany = {
      moduleId: KPIModulesEnum.Company,
      companyId: this.model.portfolioCompanyID,
      valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName
    };
    this.dataRow = rowData;
    this.dataColumns = column;
    this.isUploadPopupVisible = true;
  }
  
  getFilterAuditValue(rowdata: any, column: any){
    let auditList = this.auditLogList;
    let headers = column.field.split(' ');
    let result = [];
    if (headers?.length > 0 && auditList?.length > 0) {
      let periodHeader = null;
      let yearHeader = null;
      let monthValue = null;
      if(headers.length == 1){
        yearHeader = parseInt(headers[0]);
      } else{
        periodHeader = headers[0];
        yearHeader = parseInt(headers[1]);
        if(!periodHeader.toLowerCase().includes("q"))
        monthValue = this.miscService.getMonthNumber(periodHeader);
      }
      if (periodHeader == "Q1" || periodHeader == "Q2" || periodHeader == "Q3" || periodHeader == "Q4") {
        result = auditList.filter(x => x.quarter == periodHeader && x.year == yearHeader && x.companyKPIID == rowdata.ValuesKpiId);
      } 
      else if (monthValue != null) {
        result = auditList.filter(x => x.month == monthValue && x.year == yearHeader && x.companyKPIID == rowdata.ValuesKpiId);
      }
      else {
        result = auditList.filter(x => x.month == null && x.year == yearHeader && (x.Quarter == null || x.Quarter == '') && x.companyKPIID == rowdata.ValuesKpiId);
      }
    }
    return result;
  }
  getValues(rowdata: any, column: any) {
      let result = this.getFilterAuditValue(rowdata,column);
      if (result.length > 0) {
        return result[0].pcCompanyKPIMonthlyValueID;
      }
      else
        return 0;
  }

  CloseInfo(){
    this.infoUpdate = false;
  }

  /**
   * Handles the submit button event.
   * @param results - The results object containing the code and message.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == "ok") {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
      this.isValueUpdated = !this.isValueUpdated;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getPortfolioCompanies(null);
  }
  /**
   * Displays a success toast notification.
   * 
   * @param message - The message to be displayed in the toast.
   * @param title - The title of the toast (optional).
   * @param position - The position of the toast on the screen (optional, default: "toast-center-center").
   */
  showSuccessToast(
    message: string,
    title: string = "",
    position: string = CellEditConstants.ToasterMessagePosition
  ): void {
    this.toastrService.success(message, title, { positionClass: position });
  }

  showErrorToast(message: string, title: string = '', position: string = CellEditConstants.ToasterMessagePosition): void {
    this.toastrService.error(message, title, { positionClass: position });
  }

  /**
   * Handles the cancel button event.
   */
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }

  validateNumber(event: any,KpiInfo:string) {
    if (event.which != 15) {
      let ex: RegExp = new RegExp(/^-*\d*(?:[.,]\d{1,6})?$/);
      if (!ex.test(event.target.value)) {
        if (!Number.isInteger(Number(event.target.value) )) {
          event.target.value = parseFloat(event.target.value).toFixed(6);
        }
      }
    }
  }
  validateMaxLength(event:any): boolean {
    if (!Number.isInteger(Number(event.target.value))) {
      if (event.target.value.length == 21) return false;
    }else{
      if (event.target.value.length == 16) return false;
    }
    return true;
  }
  successToaster() {
    this.toastrService.success("Entry updated successfully", "", { positionClass: "toast-center-center" });
  }
  kpiTable_GlobalFilter(event) {
    this.companyKpiValueUnit = event?.UnitType == undefined ? {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    } : event?.UnitType;
    this.searchFilter = event;
    this.kpiCurrencyFilterModel = event;
    if(this.model?.reportingCurrencyDetail?.currencyCode != null && event?.currencyCode != null && this.model?.reportingCurrencyDetail?.currencyCode != event?.currencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
    this.getPortfolioCompanies(null);
    this.menuTrigger.closeMenu();
  }
  onConvertValueUnits(event:any)
  {
    
    this.companyKpiValueUnit=event;
    this.convertUnits();
  }
  ngAfterViewInit() {   
    if (this.uiuxMenu != undefined) {
      (this.uiuxMenu as any).closed = this.uiuxMenu.closed
      this.configureMenuClose(this.uiuxMenu.closed);
    }
  }
   configureMenuClose(old: MatMenu['close']): MatMenu['close'] {
    const upd = new EventEmitter();
    feed(upd.pipe(
      filter(event => {
        if (event === 'click') {
          return false;
        }
        return true;
      }),
    ), old);
    return upd;
  }

  onChangePeriodOption(type){
    this.filterOptions.forEach((x) => (x.key = false));
    if(type?.field == "Monthly"){
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if(type?.field == "Quarterly"){
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
    } else{
      this.isMonthly = false;
      this.isQuarterly = false;
      if(type!=undefined)
        type.key = this.isAnnually = true;
    }
    this.setDefaultTypeTab();
    this.getPortfolioCompanies(null);
  }

  selectValueTab(tab: ITab) {
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.tabName = tab.name;
    this.setPeriodsOptions(this.subSectionFields);
    this.getPortfolioCompanies(null);
  }
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      let tabList = x.body?.financialTypesModelList;
      let pageConfigTabs = this.subSectionFields;
      tabList = tabList?.filter((item: any) => pageConfigTabs?.some((otherItem: any) => item.name === otherItem.aliasName && otherItem.chartValue.length > 0));
      if(tabList != undefined && tabList.length > 0){
        this.tabValueTypeList = tabList;
        this.tabValueTypeList[0].active = true;
        this.tabName = this.tabValueTypeList[0].name;
        this.setPeriodsOptions(pageConfigTabs);
      }
    });
  }
  private setPeriodsOptions(pageConfigTabs: any[]) {
    let periodOptions = this.filterOptionsCopy;
    let activeTabData = pageConfigTabs.find(x => x.aliasName == this.tabName);
    this.filterOptions = periodOptions.filter(item => activeTabData?.chartValue?.some(otherItem => otherItem === item.field));
    let periodType = this.filterOptions.find(x => x.key);
    if(periodType == undefined){
      for(const element of periodOptions){
        element.key = false;
      }
      if(this.filterOptions.length > 0)
      {
        this.filterOptions[0].key = true;
        periodType = this.filterOptions[0];
      }
    }
    this.onChangePeriodOption(periodType);
  }
  setDefaultTypeTab = () => {
    switch (true) {
      case this.isMonthly:
        this.defaultType = PeriodTypeFilterOptions.Monthly;
        break;
      case this.isQuarterly:
        this.defaultType = PeriodTypeFilterOptions.Quarterly;
        break;
        case this.isAnnually:
        this.defaultType = PeriodTypeFilterOptions.Annual;
        break;
    }
  }  
}
function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe(
    data => to.next(data),
    err => to.error(err),
    () => to.complete(),
  );
}
