import { Component, OnInit } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-dashboard-configuration',
  templateUrl: './dashboard-configuration.component.html',
  styleUrls: ['./dashboard-configuration.component.scss']
})
export class DashboardConfigurationComponent implements OnInit {
  DashboardConfigurationTab: string = DashboardConfigurationConstants.DashboardConfigurationTab;
  ManageTrackerFieldsTab: string = DashboardConfigurationConstants.ManageTrackerFieldsTab;
  DeletedColumnTab: string = DashboardConfigurationConstants.DeletedColumnTab;
  StatusFilterTab: string = DashboardConfigurationConstants.StatusFilterTab;

  isDashboardConfigurationTab: boolean = true;

  tabList: ITab[] = [
    { name: this.DashboardConfigurationTab, active: false },
    { name: this.ManageTrackerFieldsTab, active: true },
    { name: this.DeletedColumnTab, active: false },
    { name: this.StatusFilterTab, active: false }
  ];
  selectedTab: ITab = this.tabList[1];

  constructor() {}

  ngOnInit(): void {}

  onTabClick(tab: ITab) {
    this.tabList.forEach(t => t.active = false);
    tab.active = true;
    this.selectedTab = tab;
  }
}
