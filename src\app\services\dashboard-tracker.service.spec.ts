import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DashboardTrackerService } from './dashboard-tracker.service';

const BASE_URL = 'http://localhost/';

describe('DashboardTrackerService', () => {
  let service: DashboardTrackerService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: 'BASE_URL', useValue: BASE_URL }
      ]
    });
    service = TestBed.inject(DashboardTrackerService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch dashboard data', () => {
    const mockData = [{ id: 1, name: 'Test' }];
    service.getDashboardData().subscribe(data => {
      expect(data).toEqual(mockData);
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get');
    expect(req.request.method).toBe('GET');
    req.flush(mockData);
  });

  it('should handle error', () => {
    service.getDashboardData().subscribe({
      next: () => {},
      error: (error) => {
        expect(error.status).toBe(500);
      }
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get');
    req.flush('Error', { status: 500, statusText: 'Server Error' });
  });
});
