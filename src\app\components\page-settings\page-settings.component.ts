import { Router } from '@angular/router';
import { Component, OnInit, ViewChild, ViewChildren, QueryList, ElementRef, HostListener, } from "@angular/core";
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { FormBuilder, FormGroupDirective, NgForm } from '@angular/forms';
import { ToastrService } from "ngx-toastr";
import { SubPageDetailModel } from '../page-settings/page-settings.model'
import { ComponentCanDeactivate } from 'src/app/unsaved-changes/can-deactivate/component-can-deactivate';
import { ConfirmLeaveComponent } from '../../components/page-settings/confirm-leave-component/confirm-leave.component';
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PageConfigurationPageDetails, PageConfigurationDocumentPageDetails } from 'src/app/common/enums';
import { CompanyPageSectionConstants, GlobalConstants, CompanyPageSectionCommentaryConstants } from 'src/app/common/constants';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
interface PreviousValue {
  id: string;
  value: string;
}
@Component({
  selector: "app-page-configuration",
  templateUrl: "./page-settings.component.html",
  styleUrls: ["./page-settings.component.scss"],
})
export class PageSettingsComponent
  extends ComponentCanDeactivate
  implements OnInit
{
  @ViewChild("f", { static: true }) ngform: NgForm;
  @ViewChild("f")
  form: NgForm;
  pageList: any = [];
  pageListClone: any = [];
  subPageList: any = [];
  subPageListClone: any = [];
  selectedPageItem: any = {};
  pageDropdownOptions: any = [];
  isAddFieldButtonClicked: boolean = false;
  @ViewChildren("sectioncontent") sectionContent: QueryList<ElementRef>;
  @ViewChild("f") f: FormGroupDirective;
  subPageDetailmodel: SubPageDetailModel;
  isLoader: boolean = false;
  lastSelectedPageItem: any = {};
  filterDelete: any = { isDeleted: false };
  existingTags = [];
  isPopup = false;
  modalTitle = "Confirmation";
  isSaveChagesOldCount = 0;
  onChagelastSelectedPageItem: any = {};
  isDisabledBtn: boolean = true;
  trackRecordDataTypes = [];
  pcDataTypes = [];
  pageConfigurationsDatalist = [];
  temp: number;
  disablePageList = false;
  currentModelRef: any;
  status: boolean = false;
  kpiSubPageId: number = 2;
  cabTabSubPageId: number = 38;
  otherCabTabSubPageId: number = 47;
  financial: number = 3;
  otherKpi: number = 41;
  valuationSummaryId: number = 0;
  pageConfigurationPageDetails = PageConfigurationPageDetails;
  pageConfigurationDocumentPageDetails = PageConfigurationDocumentPageDetails;
  disabled: boolean;
  inputSwitchElement: any;
  onChange: any;
  aliasNameCollection = [];
  pageConfigeDuplicateNameWarning: string =
    GlobalConstants.PageConfigeDuplicateNameWarning;
  pageConfigeFieldRequired: string = GlobalConstants.PageConfigeFieldRequired;
  commentaryPeriod: string =
    CompanyPageSectionCommentaryConstants.CommentaryPeriod;
  images: string = CompanyPageSectionConstants.Images;
  isWorkflowEnabled: boolean = false;
  isPortfolioCompanySelected: boolean ;
  ic: string = "IC";
  constructor(
    private modalService: NgbModal,
    private pageConfigurationService: PageConfigurationService,
    private router: Router,
    private fb: FormBuilder,
    private toastService: ToastrService,
    private elementRef: ElementRef,
     private portfolioCompanyService: PortfolioCompanyService,
  ) {
    super();
  }
  ngOnInit() {
    this.getConfiguration();
    this.ngform.valueChanges.subscribe(() => {});
    this.getTrackRecordDataTypes();
this.portfolioCompanyService.getWorkFlowPageConfiguration().subscribe((res) => {    
  this.isWorkflowEnabled = res;      
    });   
  }
  getTrackRecordDataTypes() {
    this.pageConfigurationService
      .getTrackRecordDataTypes()
      .subscribe((result) => {
        this.pcDataTypes = result.pageConfigurations;
        this.trackRecordDataTypes = result.pageConfigurations.filter(function (
          el
        ) {
          return el.dataType != "List";
        });
        this.pageConfigurationsDatalist = result.pageConfigurationsDatalist;
      });
  }
  canDeactivate(): boolean {
    return !this.form.form.valid || this.isDisabledBtn;
  }

  localCanDeactivate(): boolean {
    return !this.form.form.valid || this.isDisabledBtn;
  }
  OnCancel(e: any) {
    this.isPopup = false;
  }
  OnConfig(e: any) {
    this.save();
  }
  loadPopup() {
    this.isPopup = true;
  }
  ngAfterViewInit() {
    this.sectionContent.changes.subscribe(() => {
      this.sectionContent.forEach((row) => {
        let inputTextEle = row.nativeElement.querySelector("[type=text]");
        let labelEle = row.nativeElement.querySelector("label");
        inputTextEle.addEventListener("focus", () => {
          labelEle.classList.toggle("active");
        });
        inputTextEle.addEventListener("blur", () => {
          labelEle.classList.toggle("active");
        });
      });
      if (this.sectionContent && this.sectionContent.last) {
        this.isAddFieldButtonClicked &&
          this.sectionContent.last.nativeElement
            .querySelector("[type=text]")
            .focus();
        this.isAddFieldButtonClicked = false;
      }
    });
  }
  checkAnyDataChange() {
    const hasInvalidTrue = this.subPageList.some((subPage) =>
      subPage.subPageFieldList.some((field) => field.isInValid === true)
    );
    if (
      JSON.stringify(this.subPageListClone) !==
        JSON.stringify(this.subPageList) &&
      !hasInvalidTrue
    ) {
      
      this.isDisabledBtn = false;
    } else {
      this.isDisabledBtn = true;
    }
    this.checkAndDisableButtonForKeyPerformance(
      CompanyPageSectionConstants.KPI
    );
    this.checkAndDisableButtonForKeyPerformance(
      CompanyPageSectionConstants.Financials
    );
    this.checkAndDisableButtonForKeyPerformance(
      CompanyPageSectionConstants.OtherKpi
    );
  }

  previousValues: PreviousValue[] = [];
  getConfiguration() {
    this.isLoader = true;
    this.pageConfigurationService.getPageConfiguration().subscribe({
      next: (result: any) => {
        this.parseJsonResponse(result);
        this.refreshSubPageDetailList();
        this.checkAnyDataChange();
        this.isLoader = false;
        this.editMode.fill(false);
        result?.forEach((page) => {
          page?.subPageDetailList?.forEach((subPage) => {
            if(subPage?.name  === "Valuation Summary"){
              this.valuationSummaryId = subPage?.id;
            }
            subPage?.subPageFieldList?.forEach((field) => {
              this.aliasNameCollection?.push(field?.displayName.toLowerCase());
              this.previousValues?.push({
                id: field?.id,
                value: field?.displayName.toLowerCase(),
              });
            });
          });
        });
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }

  onToggleChange(isActive, j, i, isTabExpanded) {
    this.subPageList[i].subPageFieldList[j].isActive = !isActive;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }

  onListToggleChange(isListData, j, i, isTabExpanded) {
    this.subPageList[i].subPageFieldList[j].isListData = isListData;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    if (
      (this.selectedPageItem != undefined &&
        this.selectedPageItem.name == "Funds") ||
      this.selectedPageItem.name == "Portfolio Company"
    ) {
      let list = this.subPageList[i].subPageFieldList.filter(
        (x) => x.isListData
      );
      this.checkListPageToggle(list);
    } else {
      this.disablePageList = false;
    }
    this.checkAnyDataChange();
  }

  checkListPageToggle(list) {
    if (list != undefined && list.length >= 4) {
      this.disablePageList = true;
    } else {
      this.disablePageList = false;
    }
  }

  parseJsonResponse = (result: any[]) => {
    if (result == null || undefined) return;
    this.pageList = result;
    this.pageListClone = JSON.parse(JSON.stringify(this.pageList));
    this.pageDropdownOptions = this.pageList
      .filter((x) => x.isActive)
      .map((x) => ({ id: x.id, name: x.name, displayName: x.displayName }));
    this.selectedPageItem = this.pageDropdownOptions[0];
    if (Object.keys(this.lastSelectedPageItem).length != 0) {
      this.selectedPageItem = this.lastSelectedPageItem;
    }
    this.onChagelastSelectedPageItem = this.selectedPageItem;
    let list = result[0].subPageDetailList[0].subPageFieldList.filter(
      (x) => x.isListData
    );
    this.checkListPageToggle(list);
  };
  addCustomField = (item) => {
    this.isAddFieldButtonClicked = true;
    let subPageId = item.id;
    let subSection = this.subPageList.find((x) => x.id == item.id);
    this.subPageDetailmodel = <SubPageDetailModel>{};
    this.subPageDetailmodel.id = 0;
    this.subPageDetailmodel.displayName = null;
    this.subPageDetailmodel.parentId = subPageId;
    this.subPageDetailmodel.name = "Customfield";
    this.subPageDetailmodel.description = null;
    this.subPageDetailmodel.isActive = true;
    this.subPageDetailmodel.isDeleted = false;
    this.subPageDetailmodel.encryptedID = null;
    this.subPageDetailmodel.sequenceNo =
      subSection.subPageFieldList[subSection.subPageFieldList.length - 1]
        .sequenceNo + 1;
    this.subPageDetailmodel.pagePath = null;
    this.subPageDetailmodel.isCustom = true;
    this.subPageDetailmodel.dataTypeId = item.isDataType ? null : 0;
    this.subPageDetailmodel.showOnList =
      item.id != 1 && item.id !== 7 ? false : true;
    this.subPageDetailmodel.isListData =
      item.id != 1 && item.id !== 7 ? false : item.isListData;
    this.subPageDetailmodel.isHighLight =false;
    subSection.subPageFieldList.push(this.subPageDetailmodel);
    this.isSaveChagesOldCount++;
    this.addTags(subSection.subPageFieldList, false);
    if (!item.isTabExpanded) this.onTabToggle(item);
  };
  removeCustomField = (currentItem, removeFieldItem) => {
    let subSection = this.subPageList.find((x) => x.id == currentItem.id);
    let subsectionClone = this.subPageListClone.find(
      (x) => x.id == currentItem.id
    );
    if (removeFieldItem.displayName == null) {
      subSection.subPageFieldList = subSection.subPageFieldList.filter(
        (x) => x.sequenceNo != removeFieldItem.sequenceNo
      );
      subsectionClone.subPageFieldList =
        subsectionClone.subPageFieldList.filter(
          (x) => x.sequenceNo != removeFieldItem.sequenceNo
        );
      this.isSaveChagesOldCount = this.isSaveChagesOldCount - 1;
    }
    if (removeFieldItem.displayName != null) {
      let objIndex = subSection.subPageFieldList.findIndex(
        (obj) =>
          `${obj.displayName}${obj.sequenceNo}` ===
          `${removeFieldItem.displayName}${removeFieldItem.sequenceNo}`
      );
      subSection.subPageFieldList[objIndex].isDeleted = true;

      let objIndexClone = subsectionClone.subPageFieldList.findIndex(
        (obj) =>
          `${obj.displayName}${obj.sequenceNo}` ===
          `${removeFieldItem.displayName}${removeFieldItem.sequenceNo}`
      );
      subsectionClone.subPageFieldList[objIndexClone].isDeleted = true;

      if (this.isSaveChagesOldCount > 0)
        this.isSaveChagesOldCount = this.isSaveChagesOldCount - 1;
    }
    this.checkAnyDataChange();
  };
  onKeyup(item) {
    let subSection = this.subPageList.find((x) => x.id == item.id);
    this.addTags(subSection.subPageFieldList, false);
    this.hotFixForRecentChangesButtonEnabledDisable(item, subSection);
  }
  validateSubfield(field: any, currentValue: string) {
    if (this.previousValues.find((x) => x.value === currentValue)) {
      field.isInValid = true;
      this.isDisabledBtn = true;
    } else {
      field.isInValid = false;
      this.isDisabledBtn = false;
    }
  }

  onChangeSubField(item, field) {
    let currentValue = field?.displayName?.toLowerCase();
    let fieldId = field.id; // Assuming field has a unique id
    if (
      fieldId == 0 &&
      !this.previousValues.find((x) => x.value === currentValue)
    ) {
      this.previousValues?.push({ id: field?.id, value: currentValue });
    }
    this.previousValues.find((x) => x.id == fieldId).value =
      currentValue.toLowerCase();
  }
  onInput(item, field) {
    let currentValue = field?.displayName?.toLowerCase();
    let fieldId = field.id; // Assuming field has a unique id

    // Check if the value has changed
    if (
      fieldId > 0 &&
      this.previousValues.find(
        (x) => x.id == fieldId && x.value === currentValue
      ) &&
      this.previousValues.filter((item) => item.value === currentValue)
        ?.length === 1
    ) {
      let subSection = this.subPageList.find((x) => x.id == item.id);
      this.addTags(subSection.subPageFieldList, false);
      field.isInValid = false;
      this.hotFixForRecentChangesButtonEnabledDisable(item, subSection);
      return;
    }

    this.validateSubfield(field, currentValue);
  }
  hotFixForRecentChangesButtonEnabledDisable(item, subSection) {
    let subSectionClone = this.subPageListClone.find((x) => x.id == item.id);
    subSectionClone.isTabExpanded = subSection.isTabExpanded;
  }

  onTabToggle = (currentItem: any) => {
    this.subPageList
      .filter((x) => x.id != currentItem.id)
      .forEach((x) => (x.isTabExpanded = false));
    currentItem.isTabExpanded = !currentItem.isTabExpanded;
    this.addTags([], false);
  };
  collectionHas(a, b) {
    for (let i = 0, len = a.length; i < len; i++) {
      if (a[i] == b) return true;
    }
    return false;
  }
  findParentBySelector(elm, selector) {
    let all = document.querySelectorAll(selector);
    let cur = elm.parentNode;
    while (cur && !this.collectionHas(all, cur)) {
      cur = cur.parentNode;
    }
    return cur;
  }
  refreshSubPageDetailList = () => {
    if (this.pageList.length > 0) {
      let subPageDetailList = this.pageList.find(
        (x) => x.id == this.selectedPageItem.id
      ).subPageDetailList;
      this.subPageList = subPageDetailList.map((x) =>
        Object.assign(x, {
          isTabExpanded: false,
          isCustomFieldSupported: false,
        })
      );
      this.subPageListClone = JSON.parse(JSON.stringify(this.subPageList));
      this.isSaveChagesOldCount = 0;

      if (
        (this.selectedPageItem != undefined &&
          this.selectedPageItem.name == "Funds") ||
        this.selectedPageItem.name == "Portfolio Company"
      ) {
        let list = this.subPageList[0].subPageFieldList.filter(
          (x) => x.isListData
        );
        this.checkListPageToggle(list);
      } else {
        this.disablePageList = false;
      }
    }
  };
  addTags(subPageList: any[], isPage: boolean) {
    this.existingTags = [];
    if (isPage) {
      if (subPageList.length > 0) {
        subPageList.forEach((element) => {
          this.existingTags.push(element.displayName.toLowerCase());
          element?.subPageFieldList.forEach((value) => {
            if (value.displayName != null)
              this.existingTags.push(value.displayName.toLowerCase());
          });
        });
      }
    }
    if (!isPage) {
      if (subPageList.length > 0) {
        subPageList.forEach((value) => {
          if (value.displayName != null)
            this.existingTags.push(value.displayName.toLowerCase());
        });
      }
    }
  }
  isExits: any = 0;
  exitsCheckDisplayName(i, value) {
    if (value.length != 0) {
      let status = this.existingTags.indexOf(value.toLowerCase()) > -1;
      if (status) this.isExits = i;
      else this.isExits = 0;
    } else this.isExits = 0;
  }
  previousVal: any;
  currentVal: any;
  onPageDdSelectionChange = (event) => {
    this.isLoader = true;
    if (event) {
      this.previousVal = this.currentVal;
      this.currentVal = event;
    }
    if (event != undefined) {
      this.isPortfolioCompanySelected=event.name != "Portfolio Company" ? false : true;   
      this.selectedPageItem = event;
      if (event.name == "Funds" || event.name == "Portfolio Company") {     
        let list = this.subPageList[0].subPageFieldList.filter(
          (x) => x.isListData
        );
        this.checkListPageToggle(list);
      } else {
        this.disablePageList = false;
      }
    }
    this.lastSelectedPageItem = event;
    if (!this.localCanDeactivate()) {
      const modalRef = this.modalService.open(ConfirmLeaveComponent, {
        windowClass: "myCustomModalClass",
        backdrop: false,
        keyboard: false,
      });
      modalRef.componentInstance.onSave.subscribe((result: any) => {
        if (result != null && result) {
          this.pageList = JSON.parse(JSON.stringify(this.pageListClone));
          this.refreshSubPageDetailList();
          this.checkAnyDataChange();
        } else {
          this.pageDropdownOptions = this.pageList
            .filter((x: any) => x.isActive)
            .map((x: any) => ({
              id: x.id,
              name: x.name,
              displayName: x.displayName,
            }));
          this.selectedPageItem = this.pageDropdownOptions.find(
            (x: any) =>
              x.id ==
              (this.previousVal == undefined
                ? this.onChagelastSelectedPageItem.id
                : this.previousVal.id)
          );
          this.currentVal =
            this.previousVal == undefined
              ? this.onChagelastSelectedPageItem
              : this.previousVal;
        }
      });
    } else {
      this.refreshSubPageDetailList();
      this.checkAnyDataChange();
    }
    this.isLoader = false;
  };

  reset = () => {
    this.getConfiguration();
  };

  isPcCommentaryValid() {
    return this.subPageList.some(
      (element) =>
        element?.name === CompanyPageSectionCommentaryConstants.Commentary &&
        element?.subPageFieldList.some(
          (subPageField) =>
            subPageField?.name ===
              CompanyPageSectionCommentaryConstants.CommentaryPeriod &&
            subPageField?.mSubFields.some(
              (section) =>
                section?.name ===
                  CompanyPageSectionCommentaryConstants.Period &&
                section?.chartValue?.length === 0
            )
        )
    )
      ? false
      : true;
  }
  save = () => {
    this.subPageList.forEach((element) => {
      let hiddenField = element?.subPageFieldList.filter((x) => !x.isActive);
      if (
        element?.subPageFieldList?.length == hiddenField.length &&
        (element.parentId != this.pageConfigurationPageDetails.ESG  || element.name!=this.pageConfigurationDocumentPageDetails.Documents)
      ) {
        element.isActive = false;
      } else {
        if (element.parentId != this.pageConfigurationPageDetails.ESG && element.name!=this.pageConfigurationDocumentPageDetails.Documents) {
          element.isActive = true;
        }
      }
    });

    if (!this.isPcCommentaryValid()) {
      this.toastService.error(
        CompanyPageSectionCommentaryConstants.CommentaryPeriodWarning,
        "",
        { positionClass: "toast-center-center" }
      );
      this.isPopup = false;
      return false;
    }
    this.pageConfigurationService
      .pageConfigurationSaveData(this.subPageList)
      .subscribe({
        next: (result) => {
          if (result != null) {
            let message = result.message;
            if (result.code != "error")
              this.toastService.success(
                ` Page Configuration ${result.message.toLowerCase()} successfully`,
                "",
                { positionClass: "toast-center-center" }
              );
            else
              this.toastService.error(`${message}`, "", {
                positionClass: "toast-center-center",
              });
            this.isPopup = false;
            this.getConfiguration();
          }
        },
        error: (error) => {
          if (error.status == 500)
            this.toastService.error(`Empty fields are not allowed`, "", {
              positionClass: "toast-center-center",
            });
        },
      });
  };
  createCopy(orig) {
    return JSON.parse(JSON.stringify(orig));
  }
  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer !== event.container) {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else if (event.previousIndex !== event.currentIndex) {
      if (event.previousContainer === event.container) {
        moveItemInArray(
          event.container.data,
          event.previousIndex,
          event.currentIndex
        );
        this.temp = event.previousIndex;
        event.previousIndex = event.currentIndex;
        event.currentIndex = this.temp;
        const subPageID = event.container.data[event.currentIndex]["subPageID"];
        const currentID = event.container.data[event.currentIndex]["id"];
        const previousID = event.container.data[event.previousIndex]["id"];
        if (currentID != 0 && previousID != 0) {
          let localSubPage: any = event.container.data;
          let subSection = this.subPageList.find((x) => x.id == subPageID);
          let subSectionClone = this.subPageListClone.find(
            (x) => x.id == subPageID
          );
          subSectionClone.isTabExpanded = subSection.isTabExpanded;
          let objIndexCurrent = subSection.subPageFieldList.findIndex(
            (obj) => obj.id == currentID
          );
          subSection.subPageFieldList[objIndexCurrent].sequenceNo =
            localSubPage.find((x) => x.id == currentID).sequenceNo;
          let objIndexPrevious = subSection.subPageFieldList.findIndex(
            (obj) => obj.id == previousID
          );
          subSection.subPageFieldList[objIndexPrevious].sequenceNo =
            localSubPage.find((x) => x.id == previousID).sequenceNo;
        }
      }
    }
    this.checkAnyDataChange();
  }
  onToggleChartChange(
    isChart: any,
    j: string | number,
    i: string | number,
    isTabExpanded: any
  ) {
    this.subPageList[i].subPageFieldList[j].isChart = !isChart;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  onHighLightChange(
    isHighLight: any,
    j: string | number,
    i: string | number,
    isTabExpanded: any
  ) {
    this.subPageList[i].subPageFieldList[j].isHighLight = isHighLight;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  onPcLinkChange(
    isPcLink: any,
    j: string | number,
    i: string | number,
    isTabExpanded: any
  ) {
    this.subPageList[i].subPageFieldList[j].isPcLink = isPcLink;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  onChangeSubPageFields(event, j: number, i: number, isTabExpanded: any) {
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  onMainCardToggleChange(isActive, isTabExpanded, i: number) {
    this.subPageList[i].isActive = !isActive;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  editMode: boolean[] = [];
  duplicateEmptyMode: boolean[] = [];
  /**
   * Handles the click event when the edit button is clicked.
   * Sets the edit mode for the specified index to true and all other edit modes to false.
   *
   * @param index - The index of the item to be edited.
   */
  onEditClick(index: number) {
    this.editMode.fill(false);
    this.editMode[index] = true;
  }
  /**
   * Handles the outside click event for a specific index.
   * @param index - The index of the element.
   * @param aliasName - The alias name.
   */
  onOutsideClick(index: number, aliasName: string) {
    this.editMode[index] = false;
    this.checkAnyDataChange();
  }
  /**
   * Handles the keypress event.
   * Prevents input of characters that do not match the specified pattern.
   *
   * @param event - The keypress event object.
   */
  onKeypress(event: any) {
    const pattern = /^[a-zA-Z0-9[\]{}_\@.\/#&+\-\\ ]*$/;
    let inputChar = String.fromCharCode(event.charCode);
    if (!pattern.test(inputChar)) {
      event.preventDefault();
    }
  }
  /**
   * Handles the keyup event for a subPage section.
   * Updates the display name of the specified subfield and performs additional actions.
   *
   * @param page - The page object.
   * @param field - The field object.
   * @param subField - The subfield object.
   * @param aliasName - The alias name string.
   */
  onKeyupSubPageSection(page, field, subField, aliasName: string) {
    let subPage = this.subPageList?.find((x) => x.id == page.id);
    let fieldItem = subPage?.subPageFieldList?.find((x) => x.id == field.id);
    let subFieldItem = fieldItem?.mSubFields?.find(
      (x) => x.sectionID == subField.sectionID
    );
    if (subFieldItem) {
      subFieldItem.displayName = subField.aliasName;
      const remainingItems = fieldItem?.mSubFields?.filter(
        (x) => x.sectionID !== subField.sectionID
      );
      if (remainingItems.length > 0) {
        remainingItems.forEach((item) => {
          if (item.name.includes(subField.name)&&subField.name!=this.ic) {
            item.aliasName = this.replaceItem(
              item.name,
              subField.name,
              subField.aliasName
            );
          }
        });
      }
    }
    this.validateInput(subField, field);
    this.hotFixForRecentChangesButtonEnabledDisable(page, subPage);
    this.checkAnyDataChange();
  }
  /**
   * Replaces all instances of a search string with a replacement string in the provided text.
   * @param text The original text.
   * @param search The string to search for.
   * @param replacement The string to replace the search string with.
   * @returns The text with all instances of the search string replaced with the replacement string.
   */
  replaceItem(text: string, search: string, replacement: string): string {
    return text.replace(new RegExp(search, "g"), replacement);
  }
  /**
   * Checks if the given alias name is duplicated within the specified field.
   * @param aliasName - The alias name to check for duplication.
   * @param field - The field object to search for duplicate alias names.
   * @returns A boolean value indicating whether the alias name is duplicated within the field.
   */
  isDuplicateAliasName(aliasName: string, field: any): boolean {
    return (
      field.mSubFields.filter(
        (subField) =>
          subField.aliasName.toLowerCase().trim() ===
          aliasName.toLowerCase().trim()
      ).length > 1
    );
  }
  /**
   * Toggles the commentary footnote and tab expansion state for a specific sub-page.
   * @param isFootNote - A boolean indicating whether the footnote is enabled.
   * @param isTabExpanded - A boolean indicating whether the tab is expanded.
   * @param i - The index of the sub-page in the subPageList array.
   */
  onCommentaryFootnoteToggleChange(isFootNote, isTabExpanded, i: number) {
    this.subPageList[i].isFootNote = isFootNote;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
  /**
   * Checks and disables the button for the specified key performance section.
   * @param section - The name of the key performance section.
   */
  checkAndDisableButtonForKeyPerformance(section: string) {
    const keyPerformance = this.subPageList.find((x) => x.name === section);
    if (!keyPerformance) {
      return; // Early exit if keyPerformance is not found
    }
    if (this.isDisabledBtn) return;
    // Assume button is enabled; disable only if conditions are met
    this.isDisabledBtn = false;
    for (const subSection of keyPerformance.subPageFieldList) {
      for (const subField of subSection.mSubFields) {
        const isAliasNameEmpty =
          !subField.aliasName || subField.aliasName.trim() === "";
        const isAliasNameDuplicate = this.isDuplicateAliasName(
          subField.aliasName,
          subSection
        );
        if (isAliasNameEmpty || isAliasNameDuplicate) {
          this.isDisabledBtn = true;
          return; // Exit as soon as the button needs to be disabled
        }
      }
    }
  }
  isValidInput: { [key: string]: boolean } = {};
  validateInput(subField, field): void {
    const isDuplicate = this.isDuplicateAliasName(
      subField.aliasName.trim(),
      field
    );
    const isEmpty = subField.aliasName.trim() === "";
    this.isValidInput[subField.sectionID] = isDuplicate || isEmpty;
  }
  /**
   * Checks if the subField name includes any of the specified statuses.
   * @param subFieldName The name of the subField to check.
   * @returns true if subFieldName includes any of the statuses, false otherwise.
   */
  isStatusIncluded(subFieldName: string | undefined): boolean {
    const statuses = ["Actual", "LTM", "YTD"];
    return statuses.some((status) => subFieldName?.includes(status));
  }
  dropSection(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.subPageList, event.previousIndex, event.currentIndex);
    this.checkAnyDataChange();
  }
  onWorkflowToggleChange(isWorkflowEnabled: boolean) {
    this.isWorkflowEnabled = isWorkflowEnabled;
    this.checkAnyDataChange();
  }
   onToggleTrendChange(
    isTrend: any,
    j: string | number,
    i: string | number,
    isTabExpanded: any
  ) {
    this.subPageList[i].subPageFieldList[j].isTrend = !isTrend;
    this.subPageListClone[i].isTabExpanded = isTabExpanded;
    this.checkAnyDataChange();
  }
 
}
