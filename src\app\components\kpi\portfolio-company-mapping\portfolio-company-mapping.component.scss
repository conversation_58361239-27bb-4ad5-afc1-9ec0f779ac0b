@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables.scss';

.portfolio-company-mapping {
  .kpi-Search-Height {
    height: 52px !important;
    width: 100% !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border-left: none !important
  }
  .mdc-checkbox{
      display: none !important;
  }

  .drop-above {
    border-top: 1px dashed #4061C7;

  }
  .drop-below {
    border-bottom: 1px dashed #4061C7;

  }
  .drop-center {
    border: 1px dashed #4061C7;
    opacity: 1;
  }

  .mapping-sec-search {
    background: #FFFFFF 0% 0% no-repeat padding-box;
  }

  .search-text-company {
    width: 100% !important;
    border-right: none !important;
    border-left: none !important;
    height: 40px !important;
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #212121;
    opacity: 1;
    font-weight: 400;
  }

  .search {
    border-left: 1px solid #DEDFE0;
    width: 100% !important;
  }

  .action-header {
    border-left: 1px solid #DEDFE0;
    background: #FAFAFB 0% 0% no-repeat padding-box;
  }

  .action-p {
    padding-right: 20px;
  }

  .mat-custom-nodes .duplicate-kpi {
    right: 64px;
  }

  .remove-kpi {
    position: absolute;
    right: 12px !important;
    z-index: 1 !important;
  }

  .kpi-header {
    display: block !important;
  }


  .formula-image-cursor-pointer {
    cursor: pointer;
  }

  .formula-image-cursor-none {
    cursor: none;
  }

  .example-list {
    border: solid 1px #ccc;
    min-height: 60px;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    display: block;
  }

  .example-box {
    padding: 20px 10px;
    border-bottom: solid 1px #ccc;
    color: rgba(0, 0, 0, 0.87);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    cursor: move;
    background: white;
    font-size: 14px;
  }

  .cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .example-box:last-child {
    border: none;
  }

  .example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .custom-outer-padding {
    padding: 20px !important;
  }

  .margin-copyall-btn {
    margin-left: 12px !important;
    border: 1px solid #C9C9D4;
    text-align: left;
    letter-spacing: 0px;
    color: #2B2B33;
    opacity: 1;
  }

  .select-all-kpi-btn {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #C9C9D4;
    border-radius: 4px;
    text-align: left;
    letter-spacing: 0px;
    color: #2B2B33;
    opacity: 1;
  }

  .kpi-card-header {
    background: #FAFAFA 0% 0% no-repeat padding-box;
    border-bottom: 1px solid #DEDFE0;
    height: 54px !important;
    padding: 0 !important;
  }

  .custom-card-body {
    min-height: 456px !important;
  }

  .kpi-custom-border {
    border-bottom: 1px solid #DEDFE0;
  }

  .custom-node-class {
    padding-left: 16px !important;
  }

  .custom-kpi-header-style {
    text-align: left;
    letter-spacing: 0.16px;
    color: #212121;
    opacity: 1;
    display: block;
    padding-top: 18px !important;
    font-weight: 400;
    font-size: 13px !important;
  }

  .custom-p-dropdown {
    padding-top: 0px !important;
    position: absolute !important;
    float: right;
    right: 12px !important;
    top: 10px !important;
    border: none !important;
    background: #FAFAFB 0% 0% no-repeat padding-box;
    border-bottom: 1px solid #DEDFE0;
  }

  .p-dropdown {
    border: none !important;
    background: #FAFAFB 0% 0% no-repeat padding-box !important;
    border-bottom: 1px solid #DEDFE0 !important;
  }

  .custom-table-style {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    border-top: 1px solid #DEDFE0;
    border-right: 1px solid #DEDFE0;
    width: 100% !important;
    display: flex;
    justify-content: space-between;
    .float-left
    {
      width: calc(100% - 90px) !important;
    }

  }

  .custom-table-style:first-child {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    border-top: none !important;
    border-right: 1px solid #DEDFE0;
    width: 100% !important;
    display: flex;
    justify-content: space-between;

  }

  .custom-table-style:last-child {
    border-bottom: 1px solid #DEDFE0 !important;
    width: 100% !important;
    display: flex;
    justify-content: space-between;
  }

  .kpi-list-table .custom-select {
    border: none !important;
    background-color: #F0F0F1 !important;
  }

  .nep-select-inner {
    border-bottom: 1px solid #DEDFE0;
  }

  .kpi-mapping-select {
    padding-top: 10px !important;
    padding-right: 12px !important;
  }

  .combobox {

    margin: auto;
    border-bottom: 1px solid #DEDFE0;
  }

  .combobox-input {
    width: 100%;
    border: none;
    padding: 10px;
    height: 40px !important;
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #212121;
    opacity: 1;
    font-weight: 400;
    background: transparent !important;
  }

  .combobox-input:focus {

    outline-style: none;
  }

  .combobox-options {

    position: absolute;
    text-align: left;
    background-color: white;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1;
    box-shadow: 0px 3px 6px #00000015;
    border: 1px solid #DEDFE0;
    border-radius: 0px 0px 4px 4px;
    opacity: 1;
    #kpi-name{
      width: calc(100% - 90px) !important;
    }
  }

  .selected {
    background: #F7F8FC 0% 0% no-repeat padding-box;
    opacity: 1;
  }

  list-item {
    display: block;
    font-size: 14px;
    padding: 10px;
    text-align: left;
    letter-spacing: 0.17px;
    color: #212121;
    opacity: 1;
    font-weight: 500;
  }

  list-item:hover {

    background: #F7F8FC 0% 0% no-repeat padding-box;
    opacity: 1;
  }

  .error-text {
    color: rgb(165, 112, 112);
  }

  .error {
    border: 1px solid red;
  }

  .kpi-mapping-cursor-pointer {
    cursor: pointer !important;
  }

  .card-footer {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    height: 56px !important;
  }

  .card {
    box-shadow: 0px 0px 12px #00000014 !important;
  }

  .custom-space-table {
    padding-left: 20px !important;
  }
  .padding-right-add {
    padding-right: 12px !important;
    padding-left: 12px !important;
  }

  input::-webkit-input-placeholder {
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #B3B3B3;
    opacity: 1;
  }

  input:-ms-input-placeholder {
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #B3B3B3;
    opacity: 1;
  }

  input:-moz-placeholder {
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #B3B3B3;
    opacity: 1;
  }

  input::-moz-placeholder {
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #B3B3B3;
    opacity: 1;
  }

  .unkpi-card-body-height {
    height: calc(100vh - 348px) !important;
  }

  .mappedkpi-card-body-height {
    height: calc(100vh - 194px) !important;
  }

  .mat-tree-node {
    min-height: 44px !important;
  }

  .lp-report-Kpilineitems .p-multiselect {
    width: calc(100% - 114px) !important;
    max-width: 200px !important;
  }

  .kpi-footer-copy-all {
    width: calc(100% - 300px) !important;
  }

  .kpi-list-zero-state {
    height: 0 !important;
  }

  .duplicate-kpi,
  .kpi-header {
    cursor: pointer;
    display: none;
  }

  .custom-p-dropdown .p-dropdown {
    border-radius: 0px !important;
  }

  li:hover {
    .formula-image {
      display: block !important;
    }
  } 
  .search-unmapped-kpi-list{
    display: flex;
    justify-content: space-between;
  }
  .mapped-kpi-custom-width{
    max-width: 70% !important;
  }
  .custom-formula{
    padding-right: 90px !important;
  }

  // Corner down icon for child nodes
  .corner-down-icon {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px;
    vertical-align: middle;
    filter: invert(40%) sepia(0%) saturate(0%) hue-rotate(214deg) brightness(94%) contrast(90%); // #666666 equivalent
  }

  // Extraction badge
  .extraction-badge {
    display: inline-block;
    padding: 2px 8px;
    background-color: #E6EEFF;
    color: #4061C7;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
  }

  // Style for the extraction main checkbox
  .extraction-main-checkbox {
    .mdc-checkbox {
      display: inline-block !important;
    }
    
    .custom-kpi-header-style {
      padding-top: 0 !important;
      margin-left: 8px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  // Style for individual extraction checkboxes
  .extraction-checkbox {
    display: inline-block !important;
    .mdc-checkbox {
      display: inline-block !important;
    }
  }

  // Unlink extraction icon
  .unlink-extraction {
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    
    img {
      width: 24px;
      height: 24px;
      opacity: 1;
      filter: invert(40%) sepia(0%) saturate(0%) hue-rotate(214deg) brightness(94%) contrast(90%); // #666666 equivalent
      &:hover {
        opacity: 0.8;
      }
    }
  }

  // KPI action bar container
  .kpi-action-bar-container {
    position: fixed;
    bottom: 2.25rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 1000;
    height: 56px;
    align-items: center;
  }

  // KPI action bar
  .kpi-action-bar {
    background-color: $Neutral-Gray-00;
    box-shadow: $shadow-short;
    border-radius: $Radius-8;
    max-width: 100%;
    max-width: 550px;
    
    .action-bar-content {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      
      .flex-spacer {
        flex-grow: 1;
      }
      
      .selected-count {
        margin-right: 16px;
        color: $Neutral-Gray-70;
        @extend .Body-B;
        
        .info-icon {
          display: inline-block;
          vertical-align: middle;
          
          img {
            width: 16px;
            height: 16px;
          }
        }
      }
      
      .no-matching-actions{
        @extend .Body-I;
        color: $Neutral-Gray-80;
      }
      
      .extraction-action-btn {
        background-color: $primary-color-78;
        border: none;
        color: $Neutral-Gray-00;
        padding: 0.5rem 1rem;
        border-radius: $Radius-4;
        margin-right: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        
        .mat-icon {
          vertical-align: middle;
          font-size: 18px;
          height: 18px;
          width: 18px;
          line-height: 18px;
        }
        
        &.unmark-btn {
          background-color: $primary-color-78;
          
          .mat-icon {
            color: $Neutral-Gray-00; // Keep icon white for visibility on blue background
          }
        }
      }
      
      .divider-vertical {
        width: 1px;
        height: 24px;
        background-color: $neutral-divider-primary;
        margin: 0 16px 0 0;
      }
      
      .close-action {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 4px;
        
        img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }

  .mat-custom-treenode {
    min-height: 44px !important;
  }

  // Remove the general child node margin style and add specific style for extraction checkbox
  .child-extraction-checkbox {
    margin-left: 0.75rem;
  }

  // Custom styling for mat-checkbox
  mat-checkbox {
    .mdc-checkbox {
      $checkbox-size: 16px;
      width: $checkbox-size !important;
      height: $checkbox-size !important;
      
      .mdc-checkbox__background {
        border-radius: 4px !important;
        width: inherit !important;
        height: inherit !important;
      }
      
      .mdc-checkbox__checkmark {
        width: 0.5rem !important;
        height: 0.5rem !important;
        color: $Neutral-Gray-00 !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
      }
      
      .mdc-checkbox__checkmark-path {
        stroke: $Neutral-Gray-00 !important;
        stroke-width: 0.25rem !important;
        stroke-dashoffset: 0 !important;
      }
    }
  }

  // Style for checked state
  .mdc-checkbox--selected .mdc-checkbox__background,
  .mat-mdc-checkbox-checked .mdc-checkbox__background {
    background-color: $primary-color-78 !important;
    border-color: $primary-color-78 !important;
  }

  .lp-report-Kpilineitems .p-multiselect {
    width: calc(100% - 114px) !important;
    max-width: 200px !important;
  }

  // Custom styling for synonym add icon
  .synonym-add-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .custom-add-icon {
    width: 1rem !important;
    height: 1rem !important;
    line-height: 1rem !important;
    color: #666666!important;
    background-color: #ffffff !important;
    border-radius: 2px !important;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.75rem !important;
    
    &:hover {
      background-color: #EBF3FF!important;
    }
  }
  .synonym-badge {
    display: none;
    align-items: center;
    justify-content: center;
    width: 8.5rem;
    height: 2rem;
    border:1px solid #E6E6E6;
    &:hover {
      color: #4061C7!important;
      border:1px solid #4061C7;
    }
  }
  .synonym-container {
    color: #333333!important;
    &:hover {
      color: #4061C7!important;
    }
  }
  .dup-icon{
    display: none;
  }
  .mat-custom-treenode:hover .dup-icon {
    display: inline-flex !important;
  }
  .mat-custom-treenode-child:hover .dup-icon {
    display: inline-flex !important;
  }
  .mat-custom-treenode:hover .synonym-badge {
    display: inline-flex !important;
  }
  .mat-custom-treenode-child:hover .synonym-badge {
    display: inline-flex !important;
    //width: 9rem !important;
  }
}
.unmappedKpiWarningModel{
  width: 25.125rem !important;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  opacity: 1;
  top:35% !important;
}
.unmappedKpiWarningModelBody{
padding: 1rem !important;
}

.info-style{
  color: #4061C7;
  font-size: 12px;
  padding: 4px;
  background: #F5F9FF;
}
.custom-info-icon {
  font-size: 14px !important;
  color: #4061C7;
  vertical-align: middle;
  margin-right: 4px;
}

// Extraction overlay to block clicks when extraction is active
.extraction-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 990; 
  pointer-events: all;
}

// Make extraction checkboxes and action bar clickable
.extraction-checkbox,
.extraction-main-checkbox,
.kpi-action-bar-container,
.action-bar-content,
.extraction-action-btn,
.close-action,
.unlink-extraction {
  position: relative;
  z-index: 995; 
}
.custom-table-style:hover{
  background: #F5F6FB 0% 0% no-repeat padding-box;
}
.mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix{
  padding-top: 0.5rem !important;
  padding-bottom: 0px !important;
}
.mat-mdc-form-field-infix{
    min-height: 3.2rem !important;
}

 //kendo-popup{
 // z-index: 1000 !important;
//}
