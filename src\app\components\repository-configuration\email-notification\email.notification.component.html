<div class="container-fluid px-0">
    <!-- Automated Email Request Section with Expandable Header -->
    <div class="automated-email-section mb-3 pl-4 pr-4 pt-3" *ngIf="!isLoading">
        <div class="section-header d-flex justify-content-between align-items-center pb-2" (click)="toggleExpanded()"
            [ngClass]="{'border-none':  !expanded, 'pb-3': !expanded}">
            <div class="d-flex align-items-center">
                <img src="assets/dist/images/email-icon.svg" alt="Email" />
                <div class="Heading2-M ml-3">Automated Email Request</div>
            </div>
            <div>
                <img *ngIf="expanded" src="assets/dist/images/email-up-arrow-icon.svg" alt="Email" />
                <img *ngIf="!expanded" src="assets/dist/images/er-down-arrow-icon.svg" alt="Email" />
            </div>
        </div>
        <!-- Content Section - Expanded/Collapsed based on state -->
        <div class="section-content py-3" *ngIf="expanded">
            <div class="row" *ngIf="!isLoading">
                <!-- Portfolio Company Dropdown (60%) -->
                <div class="col-12 col-md-7 mb-3 portfolio-company-dropdown">
                    <div>
                        <label for="portfolioCompanySelect" class="Caption-M">Portfolio Company</label>
                        <div class="dropdown-container">
                            <kendo-multiselect class="k-multiselect-custom k-dropdown-width-100" #multiSelect
                                id="portfolioCompanySelect" [data]="portfolioCompanies"
                                [(ngModel)]="selectedPortfolioCompanies" [checkboxes]="true" [rounded]="'medium'"
                                [fillMode]="'solid'" [clearButton]="false" textField="companyName"
                                valueField="companyId" [filterable]="true" [autoClose]="false" [tagMapper]="tagMapper"
                                [kendoDropDownFilter]="filterSettings" [virtual]="virtual" placeholder="Select Company"
                                (selectionChange)="onPortfolioCompanySelectionChange()"
                                [kendoMultiSelectSummaryTag]="3">

                                <ng-template kendoMultiSelectHeaderTemplate>
                                    <div class="inline-container">
                                        <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                            kendoCheckBox [checked]="isPortfolioCompanyCheckAll"
                                            [indeterminate]="isPortfolioCompanyIndet()"
                                            (click)="onSelectAllPortfolioCompanies()" />
                                        <kendo-label>Select All</kendo-label>
                                    </div>
                                </ng-template>

                                <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                    <div class="item-container">
                                        <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">
                                            {{ dataItem.companyName }}
                                        </span>
                                    </div>
                                </ng-template>
                            </kendo-multiselect>
                        </div>
                    </div>
                </div>

                <!-- Document Type Dropdown (40%) -->
                <div class="col-12 col-md-5 mb-3 document-type-dropdown">
                    <div>
                        <label for="documentTypeSelect" class="Caption-M">Document Type</label>
                        <div class="dropdown-container">
                            <kendo-multiselect class="k-multiselect-custom k-dropdown-width-100" id="documentTypeSelect"
                                [data]="documentTypes" [(ngModel)]="selectedDocumentTypes" [checkboxes]="true"
                                [rounded]="'medium'" [fillMode]="'solid'" [clearButton]="false" textField="documentName"
                                valueField="id" [filterable]="true" [autoClose]="false" [tagMapper]="tagMapper"
                                [kendoDropDownFilter]="filterSettings" [virtual]="virtual" placeholder="Select Document"
                                [kendoMultiSelectSummaryTag]="1">

                                <ng-template kendoMultiSelectHeaderTemplate>
                                    <div class="inline-container">
                                        <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                            kendoCheckBox [checked]="isDocumentTypeCheckAll"
                                            [indeterminate]="isDocumentTypeIndet()"
                                            (click)="onSelectAllDocumentTypes()" />
                                        <kendo-label>Select All</kendo-label>
                                    </div>
                                </ng-template>
                                <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                    <div class="doc-type-item">
                                        <div class="TextTruncate Body-R pl-1">
                                            <kendo-label>
                                                {{ dataItem.documentName }}
                                            </kendo-label>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-multiselect>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    <button kendoButton class="Body-R mr-2" fillMode="outline" themeColor="primary" (click)="cancel()">
                        Cancel
                    </button>
                    <button type="button" class="width-200 nep-button nep-button-primary Body-R"
                        (click)="setupEmailReminder()">
                        Set-up Email Reminder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reminders Header -->
    <div class="d-flex justify-content-between align-items-center mb-3" *ngIf="!isLoading">
        <div class="Body-R text-black">Recent Reminder</div>
    </div> <!-- Reminder Items List -->
    <div class="reminders-list">
        <div *ngIf="!isLoading && reminderData.length === 0" class="text-center py-4">
            <div class="Body-R">No reminders found. Create a reminder to get started.</div>
        </div> <!-- Dynamic reminder list -->
        <div *ngFor="let reminder of reminderData" class="reminder-item mb-3 border rounded p-3">
            <div class="row">
                <!-- Company Details -->
                <div class="col-md-6">
                    <div class="Caption-R text-muted">Selected Company ({{reminder.companies.length}})</div>
                    <div *ngIf="reminder.companies.length === 1" class="Body-R">
                        {{reminder.companies[0].name}}
                    </div>
                    <div *ngIf="reminder.companies.length > 1" class="d-flex flex-wrap">
                        <span *ngFor="let company of reminder.companies" class="company-tag Body-R mr-2 mb-1">
                            {{company.name}}
                        </span>
                    </div>
                </div>
                <!-- Document Type and Date -->
                <div class="col-md-2">
                    <div class="Caption-M text-muted">Document Type</div>
                    <div class="Body-R doctype-text">{{reminder.documentType.name}}</div>
                </div>
                <div class="col-md-4 pr-0">
                    <div class="d-flex justify-content-between">
                        <!-- Left section with date, edit, delete -->
                        <div class="d-flex align-items-center">
                            <div class="mr-4">
                                <div class="Body-R Caption-R text-muted">Date</div>
                                <div class="Caption-M">{{reminder.date}}</div>
                            </div>
                            <div (click)="editReminder(reminder.id)" class="ml-2">
                                <img alt="Edit" src="assets/dist/images/clo_edit.svg" />
                            </div>
                            <div (click)="deleteReminder(reminder.id)" class="ml-3 custom-cursor">
                                <img alt="Delete" src="assets/dist/images/email-delete-icon.svg" />
                            </div>
                        </div>

                        <!-- Right section with status, switch, expand -->
                        <div class="d-flex align-items-center" style="margin-left: 20px;">
                            <span class="mr-2 Body-M btn btn-sm active-status">
                                {{reminderStatus[reminder.id] ? 'Active' : 'Inactive'}}
                            </span>
                            <kendo-switch [(ngModel)]="reminderStatus[reminder.id]" [onLabel]="' '" [offLabel]="' '"
                                (valueChange)="onStatusChange(reminder.id)">
                            </kendo-switch>
                            <div (click)="toggleReminderExpand(reminder.id)" class="space-lr">
                                <img [src]="expandedReminders[reminder.id] ? 'assets/dist/images/email-up-arrow-icon.svg' : 'assets/dist/images/er-down-arrow-icon.svg'"
                                    alt="Expand" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expanded content -->
            <div *ngIf="expandedReminders[reminder.id]" class="expanded-content mt-3 pt-3 border-top">
                <!-- Loading state for details -->
                <div *ngIf="!reminder.detailsData" class="text-center py-3">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="sr-only">Loading details...</span>
                    </div>
                    <div class="Body-R text-muted mt-2">Loading reminder details...</div>
                </div>

                <!-- Email reminder details component -->
                <app-email-reminder-details *ngIf="reminder.detailsData" [reminderDetails]="reminder.detailsData"
                    [reminderId]="reminder.reminderId" [isActive]="reminderStatus[reminder.id]">
                </app-email-reminder-details>
            </div> 
        </div>
    </div>
    <div *ngIf="confirmDelete" id="confirm-modal">
        <confirm-modal
            customwidth="500px"
            isCustomFooterClass="true"
            primaryButtonIsDanger="true"
            primaryButtonName="Yes, Delete"
            secondaryButtonName="No, keep it" 
            (primaryButtonEvent)="deleteEmailReminder()"
            modalTitle="{{ modalTitle }}"
            (secondaryButtonEvent)="cancelDelete()"
            isDeleteConfirmModal="true"
            isCloseEnable="true"
            (closeIconClick)="cancelDelete()"
        >
            <div class="container px-2">
                <div class="d-flex">
                    <div class="mr-13">
                    <img src="assets/dist/images/exclamation-circle-delete.svg" alt="Exclamation Circle" />
                    </div>
                    <div>
                    <div class="Heading2-M mb-1">Are You Sure ?</div>
                    <div class="Caption-R content-secondary">You wanted to delete an Active Email reminder.</div>
                    </div>
                </div>
            </div>
        </confirm-modal>
    </div>
    <app-loader-component *ngIf="isLoading"></app-loader-component>
</div>