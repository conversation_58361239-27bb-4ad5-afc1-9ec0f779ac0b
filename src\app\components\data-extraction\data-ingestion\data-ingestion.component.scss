@import "../../../../_variables";
@import "../../../../assets/dist/css/font.scss";

@mixin status-box($bg-color, $text-color) {
  background: $bg-color;
  border-radius: $Radius-28;
  padding: 2px $Spacing-12;
  color: $text-color;
  @extend .Body-R;
  width: 95px;
}

  .radio-options-group{
    padding-left: 2px; 
  }

.data-ingestion-section{
  .company-title{
    color: $Neutral-Gray-90;
    width: 100%;
  }
  .inprogress-box {
    @include status-box($Neutral-Gray-10, $Neutral-Gray-70);
  }

  .approved-box {
    @include status-box($Positive-60, $Positive-100);
  }

  .approval-box {
    @include status-box($Primary-40, $Primary-78);
    width: 140px;
  }
  .error-box {
    @include status-box($Negative-50, $Negative-100);
    width:80px;
  }
  .uploaded-box{
    width: 24px;
    height:24px;
    background: $Brand-Gradient;
    color: $Neutral-Gray-00;
    border-radius: $Radius-22;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px;
    @extend .Caption-R;
  }
    .ingestion-card{
        display: inline-block;
        border-radius: $Spacing-8;
        padding: $Spacing-8;
        width:242px;
        background: $Neutral-Gray-00;
        box-shadow: $shadow-short;
        .card-border{
            width: 4px;
            height: 30px;
            border-radius: $Radius-4;
            margin-right: 1rem;
        }
        .card-title{
            color:$Neutral-Gray-70;
            @extend .Display2-R;
        }
        .card-text{
            color:#858383;
            @extend .Body-M;
        }
        .border-left-success{
            background-color: $Positive-100;
        }
        .border-left-danger{
            background-color: $Noticeable-100;
        }
    }
    .table-section{
        border-radius: $Radius-8;
        border: 1px solid $Neutral-Gray-10;
    }
    
    .no-content-section {
        .no-content-text,.no-content-sub,
        .template-text {
            color: $Neutral-Gray-60;
            @extend .Body-R;
        }
    }
       
}
  .notification-sidebar
  {
    padding: 1rem 1.5rem;
    box-shadow:0px 0px 8px 0px #0000001F !important;
    label.req-label{
      margin-bottom: 0px !important;
      &:after {
        content: "*";
        color: $Negative-100;
        padding-left: 4px;
    }
    }
  }
  .side-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $Neutral-Gray-30;
  }
  
  .title-h{
    @extend .Heading2-R;
    color: $Neutral-Gray-70;
    padding-bottom: 0.75rem;
  }
  
  .close-icon {
    padding-bottom: 0.75rem;
    a{
      cursor: pointer;
    }
    img{
      width: 1rem;
      height: 1rem;
    }
  }
  
  .close-btn img {
    width: 16px;
    height: 16px;
  }
  
  .light-divider {
    border: 0;
    border-top: 1px solid $Neutral-Gray-05;
    margin-bottom: 12px;
    margin-top: 6px;
  }
  
  .form-container {
    display: flex;
    flex-direction: column;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 0.75rem 0rem 0.5rem 0rem !important;
    label{
      @extend .Body-R;
    }
  }

  .time-period-section{
    margin-top: 0.75rem;
    .period{
      color: $Neutral-Gray-90;
      @extend .Body-M;
    }
  }
  
  .subtitle {
    @extend .Caption-R;
    color: #666666;
    margin-bottom: 12px;
  }
  
  .date-selectors {
    display: flex;
    gap: 10px;
  }
  
  .year-dropdown,
  .month-dropdown {
    flex: 1;
  }
  .period{
    color: $Neutral-Gray-90;
    @extend .Body-M;
  }
  .custom-bottom{
    padding-bottom: 1rem;
    padding-right: 1.25rem;
  }
  .hidden-file-input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    overflow: hidden;
  }
  .files-padding{
    color: $nep-dark-grey-sub-h;
    padding: 6px 2px;
  }
  .no-content-section {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  form label{
    margin-top: 0px !important;
  }
  .state-status{
    color:$nep-dark-grey-sub-h;
  }
  .search-box-container {
    display: flex;
    display: inline-block; 
    vertical-align: middle;
    .search-label {
      font-size: 16px;
      font-weight: 500;
      white-space: nowrap;
    }
   
    .search-box-ingestion {
      display: flex;
      align-items: center;
      border: 1px solid $Neutral-Gray-10;
      border-radius: 6px;
      width: 430px !important;
      width: 100%;
      overflow: hidden;
      height: 44px;
   
      .data-ingestion-search {
        border: none;
        padding: 0.5rem;
        flex-grow: 1;
        font-size: 14px;
        color: #333333;
      }
   
      .filter-btn {
        background-color: transparent;
        border: none;
        padding: 0.5rem 0.75rem;
        cursor: not-allowed;
        border-left: 1px solid $Neutral-Gray-10;
        height: 44px;
        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;  
  padding-left: 28px;
}

.search-icon {
  position: absolute;
  left: 10px;
  z-index: 1;
  pointer-events: none;
  width: 12px;
  height: 12px;
}

.data-ingestion-search {
  padding-left: 35px;
  width: 100%;
}
.v-align-middle{
  vertical-align: middle;
}
.ingestion-btn{
  line-height: 44px;
  @extend .v-align-middle;
}

$padding-sizes: (0, 2, 4, 8, 12, 16, 20, 24, 32);

// All sides padding
@each $size in $padding-sizes {
  .padding-#{$size} {
    padding: #{$size}px;
  }
}

// Direction-specific padding
@each $size in $padding-sizes {
  .padding-top-#{$size} {
    padding-top: #{$size}px;
  }
  
  .padding-right-#{$size} {
    padding-right: #{$size}px;
  }
  
  .padding-bottom-#{$size} {
    padding-bottom: #{$size}px;
  }
  
  .padding-left-#{$size} {
    padding-left: #{$size}px;
  }
  
  .padding-x-#{$size} {
    padding-left: #{$size}px;
    padding-right: #{$size}px;
  }
  
  .padding-y-#{$size} {
    padding-top: #{$size}px;
    padding-bottom: #{$size}px;
  }
}
.k-radio{
  border-color: $nep-button-primary !important;
  padding: 2px;
}
.extraction-title {
  color: $Neutral-Gray-60;
}

.extraction-radio-btn {
  color: $nep-dark-h;
  padding: 4px;
}
.custom-info{
  padding-left: 4px;
}
.next-stepper{
  height: calc(100vh - 172px);
  overflow-y: auto;
}
.error-message {
  color: $Noticeable-110;
  border-radius: 4px;
  background: $Noticeable-50;
  bottom: 60px;
}
.kpi-error-message {
  color: #DE3139;
  border-radius: 4px;
  background: #FFEDF0;
  bottom: 60px;
}
.custom-configbtn{
  position: absolute;
  right: 285px;
}
.card-divider{
  border-top: 1px solid #F2F2F2;
  opacity: 1;
  margin: 12px 0px !important;
}
.specific-kpi-note {
    color: #4061C7;
    border-radius: 4px;
    background: #EBF3FF;
    bottom: 60px;
}
.file-size-warning {
    color: #666666;
    border-radius: 4px;
    background: #FAFAFA;
    bottom: 60px;
    position: fixed;
    font-style: italic;
}
input[type="checkbox"] {
  accent-color: #666666;
  border: 1px solid #666666;
}