import {  
  DecimalDigitEnum,
} from "../services/miscellaneous.service";
import { NotificationSettings } from "@progress/kendo-angular-notification";
export class GlobalConstants {
  public static USDCurrencyCode: string = "USD";
  public static SomethingWentWrong: string = "Something went wrong";
  public static  RevertSuccess:string="Changes reverted successfully";
  public static Manual = "Manual";
  public static Actual = "actual";
  public static Budget = "budget";
  public static fund = "fund";
  public static strategy = "strategy";
  public static region = "region";
  public static country = "country";
  public static status = "status";
  public static privateOrpublic = "privateOrpublic";
  public static months = "months";
  public static firm = "firm";
  public static deal = "deal";
  public static company = "company";
  public static module = "module";
  public static datatype = "datatype";
  public static calctype = "calctype";
  public static periodtype = "periodtype";
  public static KpiFillter = {
    first: 0,
    rows: 1000,
    globalFilter: null,
    multiSortMeta: [
      { field: "year", order: -1 },
      { field: "month", order: -1 },
    ],
    sortOrder: -1,
  };
  public static  AuditLogError:string="Audit log not supported for headers, converted and formula values";
  public static  CellEditError:string="Cell edit not supported for headers and formula values";
  public static  CapTableCellEditError:string="Cell edit not supported for headers values";
  public static  FinancialsCellEditError:string="Cell edit not supported for headers, formula and converted values";
  public static  AuditLogNAMessage:string="Audit log not supported for empty values with no audit history";
  public static readonly FinancialsKpiAuditUrl="/audit-logs";
  public static readonly ProfitandLossAuditLocalStorage="ProfitandLoss-auditLogData";
  public static readonly BalanceSheetAuditLocalStorage="BalanceSheet-auditLogData";
  public static readonly CashFlowAuditLocalStorage="CashFlow-auditLogData";
  public static readonly CurrentModule="CurrentModule";
  public static readonly ImpactKpiAuditLocalStorage="ImpactKpi-auditLogData";
  public static readonly OperationalKpiAuditLocalStorage="OperationalKpi-auditLogData";
  public static readonly CompanyKpiAuditLocalStorage="CompanyKpi-auditLogData";
  public static readonly CapTableAuditLocalStorage="CapTable-auditLogData";
  public static  CurrencyRateError:string="Please select a currency to get the spot rate.";
  public static  SpotRateError:string="Conversion rate not available for the selected date.";
  public static  SpotRateRetrievalError:string="Error retrieving spot rate.";
  public static  AuditLogErrorForConvertedValue:string="Audit log is not supported for headers, formulas, converted values.";
  public static  EditgErrorForConvertedValue:string="Editing cells is not supported for headers, formulas, converted values.";
  public static  EditSpotRateConversionError:string="Editing cells is not supported for spot conversions.";
  public static  AuditLogSpotRateConversionError:string="Audit log is not supported for spot conversions.";
  public static  AuditLogTitle:string="Switch to view cell based audit trails.";
  public static readonly onlyNumericValuesAllowed = "Only numeric values allowed";
  public static readonly onlyOneMinusSignAllowed = "Only one minus sign is allowed and it must be at the beginning";
  public static readonly valueExceedsLimit = "Entered value exceeds the limit";
  public static  StaticInfoSaveWarningMessage:string="Note : Changes made on this page need to be replicated under dashboard too";
  public static  PageConfigeDuplicateNameWarning:string="Field with same name already exists.Please use a different name";
  public static  PageConfigeFieldRequired:string="Field name cannot be left blank";
  public static  SDGImageUploadSuccess:string="Images uploaded successfully!";
  public static  SDGImageUploadFailure:string="Failed to upload images.";
  public static  ParentChildWarningNote:string="Note : Only one parent child relationship can be selected for graph creation";
  public static  ParentChildError:string="Only one parent child relationship can be selected for graph creation";
  public static  ParentChildChkDisabled:string="Option disabled : Multiple selection detected. Choose single KPI Type or line item";
  public static readonly StaticInfoEnableWorkflow:string="Enable Workflow";
}
export class CommonConstants{
  public static readonly  FormRequiredMessage:string="Please select the required fields";
  public static readonly  LocationExists:string="Same location is already added for the firm";
  public static readonly  AsOfDateText:string = 'As of Date';
 public static quarterOptions: any = [
      { value: "Q1", text: "Q1", number: 1 },
      { value: "Q2", text: "Q2", number: 2 },
      { value: "Q3", text: "Q3", number: 3 },
      { value: "Q4", text: "Q4", number: 4 },
    ];
  public static monthOptions: any = [
      { value: "Jan", text: "January", number: 1 },
      { value: "Feb", text: "February", number: 2 },
      { value: "Mar", text: "March", number: 3 },
      { value: "Apr", text: "April", number: 4 },
      { value: "May", text: "May", number: 5 },
      { value: "Jun", text: "June", number: 6 },
      { value: "Jul", text: "July", number: 7 },
      { value: "Aug", text: "August", number: 8 },
      { value: "Sep", text: "September", number: 9 },
      { value: "Oct", text: "October", number: 10 },
      { value: "Nov", text: "November", number: 11 },
      { value: "Dec", text: "December", number: 12 }]
}
export class RepositoryConstants {
  public static sharedFolderType: string = "Shared Folder";
  public static privateFolderType: string = "Private Folder";
  public static generalFolderType: string = "General Folder";
  public static recycledFolderType: string = "Recycle Bin";
  public static uploadedFolderType: string = "Uploaded Files";
  public static finalFolderType: string = "Final Files";
  public static Uploaded: string = "Uploaded";
  public static Final: string = "Final";
}



export class KpiTypesConstants {
  public static OPERATIONAL_KPI: string = "Operational KPI";
  public static INVESTMENT_KPI: string = "Investment KPI";
  public static COMPANY_KPI: string = "Company KPI";
  public static IMPACT_KPI: string = "Impact KPI";
  public static CapTable_KPI1: string = "Cap Table1";
  public static CapTable_KPI2: string = "Cap Table2";
  public static CapTable_KPI3: string = "Cap Table3";
  public static CapTable_KPI4: string = "Cap Table4";
  public static CapTable_KPI5: string = "Cap Table5";
  public static PROFIT_LOSS_KPI: string = "Profit & Loss KPI";
  public static BALANCE_SHEET_KPI: string = "Balance Sheet KPI";
  public static CASHFLOW_KPI: string = "Cashflow KPI";
  public static MonthlyReport: string = "Monthly Report";
  public static CREDIT_KPI: string = "Credit KPI";
  public static TRADING_RECORDS: string = "Trading Records";
  public static kpiMaxLength: number = 200;
  public static descritionMaxLength: number = 1000;
  public static kpiMaxLengthEsg: number = 800;
  public static FUND_FINANCIALS: string = "Fund Financials";
  public static FUND_FINANCIALS1: string = "Fund Financials1";
  public static FUND_FINANCIALS2: string = "Fund Financials2";
  public static FUND_FINANCIALS3: string = "Fund Financials3";
  public static FUND_FINANCIALS4: string = "Fund Financials4";
  public static FUND_FINANCIALS5: string = "Fund Financials5";
  public static FUND_FINANCIALS6: string = "Fund Financials6";
  public static FUND_FINANCIALS7: string = "Fund Financials7";
  public static FUND_KEY_FINANCIALS1: string = "Fund Key Financials1";
  public static FUND_KEY_FINANCIALS2: string = "Fund Key Financials2";
  public static FUND_KEY_FINANCIALS3: string = "Fund Key Financials3";
  public static FUND_KEY_FINANCIALS4: string = "Fund Key Financials4";
  public static FUND_KEY_FINANCIALS5: string = "Fund Key Financials5";
  public static FUND_KEY_FINANCIALS6: string = "Fund Key Financials6";
  public static FUND_KEY_FINANCIALS7: string = "Fund Key Financials7";
  public static FUND_KEY_FINANCIALS8: string = "Fund Key Financials8";
}

export function isFundFinancialsType(name: string): boolean {
  return name === KpiTypesConstants.FUND_FINANCIALS ||
         name === KpiTypesConstants.FUND_FINANCIALS1 ||
         name === KpiTypesConstants.FUND_FINANCIALS2 ||
         name === KpiTypesConstants.FUND_FINANCIALS3 ||
         name === KpiTypesConstants.FUND_FINANCIALS4 ||
         name === KpiTypesConstants.FUND_FINANCIALS5 ||
         name === KpiTypesConstants.FUND_FINANCIALS6 ||
         name === KpiTypesConstants.FUND_FINANCIALS7 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS1 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS2 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS3 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS4 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS5 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS6 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS7 ||
         name === KpiTypesConstants.FUND_KEY_FINANCIALS8 ||
         name?.startsWith("Fund Financials") ||
         name?.startsWith("Fund Key Financials");
}

export class KpiMappingConstants {
  public static searchKpiButton: string = "searchKpiButton";
  public static searchKpiModal: string = "searchKpiModal";
  public static emptyItem: string = "emptyItem";
  public static above: string = 'above';
  public static below: string = 'below';
  public static center: string = 'center';
  public static OnlyOneLevelOfHierarchyIsAllowed: string = "Only one level of hierarchy is allowed";
  public static ToasterMessagePossition:string="toast-center-center";
  public static MultipleDuplicateNotAllowed: string = "Multiple Duplicate Child KPIs not allowed under same parent";
  public static MappedKpiSuccessfullywithcompany:string =  "Selected KPI(s) are mapped successfully with the company";
  public static ErrorOccured:string =  "Error occured";
  public static ProfitLossKPI:string = "Profit & Loss KPI";
  public static ProfitAndLossKPI:string = "Profit And Loss KPI";
  public static DuplicateKPICreatedSuccessfully:string = "Duplicate KPI created successfully";
  public static ErrorOccurredCreatedDuplicateKPI:string = "Error occurred to create duplicate kpi";
  public static Add:string = 'Add';
  public static Escape:string ='Escape';
  public static DuplicateKpiMappedSameCompany:string = 'Duplicate parent KPI mapped with same company';
  public static Enter:string = 'Enter';
  public static PX:string = "px";
  public static AddSynonym:string = 'Add Synonym';
  public static EditSynonym:string = 'Edit Synonym';
  public static Update:string = 'Update';
  public static SynonymUpdatedSuccessfully:string = 'The synonyms have been updated successfully';
  public static SynonymAddedSuccessfully:string = 'KPI synonyms have been successfully added';
}
export class KpiTypesPrefixConstants {
  public static OPERATIONAL_KPI_PREFIX: string = "Opr_";
  public static INVESTMENT_KPI_PREFIX: string = "Inv_";
  public static COMPANY_KPI_PREFIX: string = "Com_";
  public static IMPACT_KPI_PREFIX: string = "Imp_";
  public static PROFIT_LOSS_KPI_PREFIX: string = "Pro_";
  public static BALANCE_SHEET_KPI_PREFIX: string = "Bal_";
  public static CASHFLOW_KPI_PREFIX: string = "Cas_";
  public static CREDIT_KPI_PREFIX: string = "Cre_";
  public static TRADING_RECORDS_PREFIX: string = "Tra_";
  public static CapTable1_PREFIX: string = "Cap1_";
  public static CapTable2_PREFIX: string = "Cap2_";
  public static CapTable3_PREFIX: string = "Cap3_";
  public static CapTable4_PREFIX: string = "Cap4_";
  public static CapTable5_PREFIX: string = "Cap5_";
}
export class KpiConstants {
  public static INVESTMENT_KPI: string = "Investment KPIs";
}
export function GetKpiTypes() {
  return [
      { name: KpiTypesConstants.OPERATIONAL_KPI, field:"Operational" },
      { name: KpiTypesConstants.INVESTMENT_KPI, field:"Investment" },
      { name: KpiTypesConstants.COMPANY_KPI , field:"Company"},
      { name: KpiTypesConstants.IMPACT_KPI, field:"Impact" },
      { name: KpiTypesConstants.PROFIT_LOSS_KPI, field:"ProfitAndLoss" },
      { name: KpiTypesConstants.BALANCE_SHEET_KPI, field:"BalanceSheet" },
      { name: KpiTypesConstants.CASHFLOW_KPI, field:"CashFlow" },
      { name: KpiTypesConstants.TRADING_RECORDS, field:"TradingRecords" },     
      
  ];
}
export function GeFundStatusTypes() {
  return [
      { name:"Unrealized"},
      { name:"Realized"},
      { name:"Partially Realized"},
      { name:"All"}
  ];
}
export function GetKpiInfoTypes() {
  return [
      { name: "Currency" },
      { name: "Number" },
      { name: "%"},
      { name: "Text" },
      { name: "x" },
  ];
}

export function GetReportTemplateActions() {
  return [
      { name: "Choose action", status: "" },
      { name: "Set as Active", status: "A" },
      { name: "Set as Inactive", status: "I" },
      { name: "Rename template", status: "R" },
      { name: "Delete template", status: "D" },
  ];
}
export function secureRandom(seed:number){
  const randomBytes = new Uint32Array(1);
  crypto.getRandomValues(randomBytes);
  return randomBytes[0] % seed;
}
export function GetReportTemplatePreferenceOptions() {
  return [
      { name: "Excel Template",  isActive: true },
      { name: "Funds", isActive: false },
      { name: "Sections", isActive: false },
      { name: "Data Type", isActive: false },
      { name: "Calculations", isActive: false },
      { name: "Period Type",  isActive: false },
      { name: "Period",  isActive: false },
      { name: "Currency Unit",  isActive: false },
      { name: "Group",  isActive: false }
  ];
}
export function GetConsolidatedReportTemplatePreferenceOptions() {
  return [
      { name: "Funds", isActive: true },
      { name: "Sections", isActive: false },
      { name: "Excel Template",  isActive: false },
  ];
}
export function GetDataTypeOptions() {
  return [
      { name: "Actual Period", isActive: false },
      { name: "Future Period", isActive: false }
  ];
}
export function GetTemplatePeriodOptions() {
  return [
      { name: "Template Period", value:1 },
      { name: "Custom Period", value:2 }
  ];
}
export function GetTemplateUnits() {
  return [
      { name: "Absolute", value:0 },
      { name: "Thousands", value:1 },
      { name: "Millions", value:2 },
      { name: "Billions", value:3 }
  ];
}
export  class ReportTemplatePreferenceConstants {
  public static ExcelTemplate: string = "Excel Template";
  public static Funds: string = "Funds";
  public static Sections: string = "Sections";
  public static DataType: string = "Data Type";
  public static Calculations: string = "Calculations";
  public static PeriodType: string = "Period Type";
  public static Periods: string = "Period";
  public static CurrencyUnit: string = "Currency Unit";
  public static Group: string = "Group";
}
export  class ReportTypeConstants {
  public static InternalReport: string = "Internal Report";
  public static ConsolidatedReport: string = "Fund of Fund Report";
  public static MonthlyReport: string = "Monthly Report";
  public static GrowthReport: string = "Growth Report";
  public static InternalReportId: number = 1000;
  public static ConsolidatedReportId: number = 1001;
  public static MonthlyReportId: number = 1002;
  public static GrowthReportId: number = 1003;
}
export class ReportTemplateConstants {
  public static Active: string = "Set as Active";
  public static InActive: string = "Set as Inactive";
  public static Rename: string = "Rename template";
  public static Delete: string = "Delete template";
  public static Action: string = "Choose action";
}
export class TemplateSections {
  public static Default: string ="Default Template";
  public static InvestmentTable: string = "KPI - Investment Table";
  public static InvestmentGraph: string = "KPI - Investment Graph";
  public static CompanyTable: string = "KPI - Company Table";
  public static CompanyGraph: string = "KPI - Company Graph";
  public static OperationalTable: string = "KPI - Operational Table";
  public static OperationalGraph: string = "KPI - Operational Graph";
  public static CreditTable: string = "KPI - Credit Table";
  public static CreditGraph: string = "KPI - Credit Graph";
  public static ImpactTable: string = "KPI - Impact Table";
  public static ImpactGraph: string = "KPI - Impact Graph";
  public static TradingTable: string = "KPI - Trading Record Table";
  public static TradingGraph: string = "KPI - Trading Record Graph";
  public static CompanyFinancials: string = "Company Financials";
}
export class FundEnum{
  public static  StaticData:string="Static Data";
  public static  StrategyDescription:string="Strategy Description";
  public static  FPChart:string="Fund Performance Chart";
  public static  SWTotalValueChart:string="Sector Wise Total Value Chart";
  public static  SWInvestmentValueChart:string="Sector Wise Investment Cost Chart";
  public static  TrackRecord:string="Track Record";
  public static AttributionReportTable:string="Attribution Report Table";
  public static AttributionReportGraph:string="Attribution Report Graph";
  public static FilterAddedSuccesfully: string = "Filter added succesfully"
  public static FilterDeletedSuccesfully: string = "Filter deleted succesfully"
}
export class NotificationConstants {
  public static VAPID_PUBLIC_KEY : string  = 'BJWor3koKGUnN9-HC7WMGNjIGLqpIL9i9ZGKMbnVyFPUgqrIXIhrwMFghFJ_9D-i3ejJbTMt5RzDnrS9-Dxzupo';
}

export function GeFundCashflowHeaders() {
  return [
      { header:"Company Name",field:"name"},
      { header:"Transaction Date",field:"Date"},
      { header:"Transaction Type",field:"Transaction Type"},
      { header:"Transaction Value",field:"value"}
  ];
}
export function GeFundCashflowReportingCurrencyTableHeaders() {
  return [
      { header:"Company Name",field:"name"},
      { header:"Reporting Currency",field:"currency"},
      { header:"Transaction Date",field:"date"},
      { header:"Transaction Type",field:"transactionType"},
      { header:"Transaction Value",field:"value"}
  ];
}

export function GeFundPerformanceHeaders() {
  return [
      { header:"Capital Invested",field:"capitalInvested"},
      { header:"Realized Value",field:"realizedValue"},
      { header:"Unrealized Value",field:"unrealizedValue"},
      { header:"Total Value",field:"totalValue"},
      { header:"Gross IRR",field:"IRR"},
      { header:"Gross TVPI",field:"TVPI"}
  ];
}

export function GeFundPerformanceReportingHeaders() {
  return [
      { header:"Reporting Currency",field:"currency"},
      { header:"Capital Invested",field:"capitalInvested"},
      { header:"Realized Value",field:"realizedValue"},
      { header:"Unrealized Value",field:"unrealizedValue"},
      { header:"Total Value",field:"totalValue"},
      { header:"Gross IRR",field:"irr"},
      { header:"Gross TVPI",field:"tvpi"}
  ];
}

export class FundCashflowConstants{
  public static Realised:string="Realised";
  public static Unrealised:string="Unrealised";
  public static All:string="All";
  public static Others:string="Others";
  public static FundCurrency:string="Fund Currency";
  public static ReportingCurrency:string="Reporting Currency";
  public static Name:string="name";
  public static Value:string="value";
  public static Date:string="Date";
  public static TransactionType:string="Transaction Type";
  public static IsRealizedValue:string="isRealizedValue";
  public static PortfolioCompanyId:string="portfolioCompanyId";
  public static FeesOrExpense:string="Fees/Expense";
  public static Fees:string="Fees";
  public static IsExpense:string="isExpense";
  public static Currency:string="currency";
  public static CapitalInvested:string="Capital Invested";
  public static RealizedValue:string="Realized Value";
  public static UnrealizedValue:string="Unrealized Value";
  public static IRR:string="IRR";
  public static TVPI:string="TVPI";
  public static TotalValue:string="Total Value";
  public static Total:string="Total";
  public static IsTotal:string="isTotal";
  public static TotalRealized:string="Total Realized";
  public static TotalUnRealized:string="Total Unrealized";
  public static CompanyName:string="Company Name";
  public static FundCashflow:string="Fund Cashflow";
  public static FundPerformance:string="Fund Performance";
}
export class DataAnalyticsConstants{
  public static InvestmentKpiURL:string="/api/get/investment/investment-kpi-values";
  public static OperationalKpiURL:string="/api/get/operational-kpi-values";
  public static TradingAndCompanyURL:string="/api/data-analytics/data-source";
  public static ProfitAndLossURL:string="/api/Financials/data-analytics/profitandloss";
  public static BalanceSheetURL:string="/api/Financials/data-analytics/balancesheet";
  public static CashFlowURL:string="/api/Financials/data-analytics/cashflow";
  public static getDealDataSourceURL:string = "/api/data-analytics/deals/V1";
  public static GetFundsByInvestorURL:string = "/data-analytics/filters/funds"; 
  public static getFundsDataSourceURL:string = "/api/data-analytics/funds/V1";
  public static getInvestorsDataSourceURL:string = "/api/data-analytics/investor";
  public static getPCDetailsDataSourceURL:string = "/api/data-analytics/bi/portfolio-company";
  public static getESGDetailsDataSourceURL:string = "/api/data-analytics/esg";
  public static SubTitle:string = "Excel2Json";
  public static PostMethod = "POST";
  public static PeriodTypeLast1Year = "1 YR (Last 1 year)";
  public static PeriodTypeDateRange = "Date Range";
  public static PeriodTypeCustom = "Custom";
  public static PCDetails = "PC Informations";
  public static ESGDetails = "ESG Informations";
  public static DealDetails = "Deal Informations";
  public static FundDetails = "Fund Informations";
  public static InvestorDetails = "Investor Informations";
  public static FileSize = "File size is greater than 20 MB";
  public static MaxFileSize = 20;
  public static KiloByteSize = 1024;
  public static CancelFileProgress = "Cancel File Progress";
  public static RestDataSourceItem = "RestDataSourceItem";
  public static JsonDataSourceItem = "JsonDataSourceItem";
  public static CompanyName = "CompanyName";
  public static FundName = "FundName";
  public static InvestorName = "InvestorName";
  public static FundId = "FundId";
  public static InvestorId = "InvestorId";
  public static PCFundHoldingDetails = "PortfolioCompanyFundHoldingDetails";
  public static StaticInformation = "StaticInformation";
  public static FundStaticInformation = "FundStaticInformation";
  public static BasicDetails ="BasicDetails";
  public static StaticFundDetails ="FundDetails";
  public static FundTerms = "FundTerms";
  public static GeographicLocations = "GeographicLocations";
  public static TrackRecord = "TrackRecord";
  public static StaticDateFormat = "yyyy-MM-dd";
  public static NewDashboardTitle = "New Dashboard";
  public static deleteModalBody = "This action will delete the current dashboard. Are you sure you want to delete?";
  public static primaryDeleteButtonName = "Confirm";
  public static secondaryDeleteButtonName = "Cancel";
  public static deleteModalTitle = "Delete Dashboard";
  public static tabType = "tabType";
  public static isCreateDashboard = "isCreateDashboard";
  public static dataAnalyticsUploadModel = "dataAnalyticsUploadModel";
  public static SomethingWentWrong = "Something went wrong";
  public static GeneralTabName = "General";
  public static CommonTabName = "Common";
  public static MyDashboardTabName = "My Dashboard";
  public static edit = "edit";
  public static Dashboard = "Dashboard";
  public static Admin =  "Admin";
  public static PageConfig = "PageConfig";
  public static Operational_KPIs = "Operational KPIs"
  public static PortfolioCompany = "PortfolioCompany";
  public static Fund = "Fund";
  public static Deal = "Deal";
  public static Investor = "Investor";
  public static ESG = "ESG";
  public static Company = "Company";
  public static YearRange = "2000:2050";
  public static Reset = "reset";
  public static InvestorList: "investorList";
  public static ToastCenterCenter: "toast-center-center";

}
export class WorkflowConstants{
  public static readonly TotalMarkedForRework: string = "Marked for Rework";
  public static readonly DiscardMessage: string = "This will delete the draft and the changes made. Are you sure you want to discard?";
  public static readonly TotalMarkedForReview: string = "Marked for Review";
  public static readonly TotalMarkedAsApproved: string = "Marked as Approved";
  public static readonly SubmitConstantsBetweenProcess: string = "Once submitted, portfolio company data cannot be edited again until current draft gets finalised/rejected. Are you sure?";
  public static readonly SubmitConstantsFinalProcess: string = "Once submitted, portfolio company draft data will be published/rejected. Are you sure?";
  public static readonly SectionEditingMessage: string = "Sections loaded for editing successfully";
  public static readonly SectionEditingSuccessfullMessage: string = "Sections available for editing updated successfully";
  public static readonly MarkedForReviewMessage: string = "Data edited and marked for review successfully";
  public static readonly MarkedForApprovalMessage: string = "Data marked for approval successfully";
  public static readonly MarkedForReworkMessage: string = "Data marked for rework successfully";
  public static readonly MarkedForPublishMessage: string = "Data marked for publishing successfully";
  public static readonly MarkedForRejectMessage: string = "Data marked for rework successfully";
  public static readonly PublishConfirmMessage: string = "Once submitted, portfolio company draft data will be published. Are you sure?";
  public static readonly SectionDeselection: string = "Modified data will be removed on deselection";
}

export function GetFundcashflowTypes() {
  return [{name:FundCashflowConstants.Realised},{name:FundCashflowConstants.Unrealised},{name:FundCashflowConstants.Others},{name:FundCashflowConstants.All}]
}

export class CompanyPageSectionConstants {
  public static readonly CompanyInformation: string = "Static Information";
  public static readonly KPI: string = "Key Performance Indicator";
  public static readonly Financials: string = "Company Financials";
  public static readonly OtherKpi: string = "Other KPIs";
  public static readonly Commentary: string = "Commentary";
  public static readonly InvestmentProfessionals: string = "Investment Professionals";
  public static readonly Locations: string = "Geographic Locations";  
  public static readonly Images: string = "Images";
}
export class CompanyPageSectionCommentaryConstants {
  public static readonly Commentary: string = "Commentary";
  public static readonly CommentaryPeriod: string = "Commentary Period";
  public static readonly Period: string = "Period";
  public static readonly CommentaryPeriodWarning: string = "Atleast one commentary period needs to be selected by default";
}
export class CommonPCConstants {
  public static readonly TaaboHost: string = "taabo-ch.beatfoliosure.com";
  public static readonly ExeterHost: string = "exeter.beatfoliosure.com";
  public static readonly BristolHost: string = "bristol.beatfoliosure.com";
  public static readonly MonmouthHost: string = "monmouth.beatfoliosure.com";
  public static readonly PizarroHost: string  = "pizarro.beatfoliosure.com";
  public static readonly TrialHost: string  = "trial.beatfoliosure.com";
  public static readonly TestHost: string  = "test.beatapps.net";
  public static readonly UATHost: string  = "uat.beatapps.net";
  public static readonly HimeraHost: string  = "himera-staging.beatfoliosure.com";
  public static readonly FeatureHost: string = "dev.beatapps.net";
    /**
   * Host URL for the feature development environment.
   */
}
export class DealDetailsConstants {
  public static readonly DealCustomID = "DealCustomID";
  public static readonly FundName = "FundName";
  public static readonly CompanyName = "CompanyName";
  public static readonly Currency = "Currency";
  public static readonly InvestmentDate = "InvestmentDate";
  public static readonly EntryMultiple = "EntryMultiple";
  public static readonly EntryOwnershipPercent = "EntryOwnershipPercent";
  public static readonly CurrentExitOwnershipPercent = "CurrentExitOwnershipPercent";
  public static readonly EnterpriseValue = "EnterpriseValue";
  public static readonly EmployeeName = "EmployeeName";
  public static readonly LeadEmployeeName = "LeadEmployeeName";
  public static readonly BoardSeat = "BoardSeat";
  public static readonly ExitMethod = "ExitMethod";
  public static readonly InvestmentStage = "InvestmentStage";
  public static readonly SecurityType = "SecurityType";
  public static readonly DealSourcing = "DealSourcing";
  public static readonly TransactionRole = "TransactionRole";
  public static readonly ValuationMethodology = "ValuationMethodology";
  public static readonly DealStatus = "Status";
  public static readonly Customfield = "Customfield";
  public static readonly PCInvestmentDate = "PCInvestmentDate";
}
export class CompanyInformationConstants {
  public static readonly BusinessDescription: string = "BussinessDescription";
  public static readonly CompanyName: string = "CompanyName";
  public static readonly MasterCompanyName: string = "MasterCompanyName";
  public static readonly FundId: string = "FundId";
  public static readonly DealId: string = "DealId";
  public static readonly CompanyLogo: string = "CompanyLogo";
  public static readonly CompanyStatus: string = "Status";
  public static readonly StockExchange_Ticker: string = "StockExchange_Ticker";
  public static readonly Website: string = "Website";
  public static readonly Sector: string = "Sector";
  public static readonly SubSector: string = "SubSector"; 
  public static readonly FinancialYearEnd: string = "FinancialYearEnd";
  public static readonly IMPACT_KPI: string = "Impact KPI";
  public static readonly Currency: string = "Currency";
  public static readonly PROFIT_LOSS_KPI: string = "Profit & Loss KPI";
  public static readonly BALANCE_SHEET_KPI: string = "Balance Sheet KPI";
  public static readonly CASHFLOW_KPI: string = "Cashflow KPI";
  public static readonly TRADING_RECORDS: string = "Trading Records";
  public static readonly SignificantEvents: string = "Significant Events";
  public static readonly AssessmentPlan: string = "Assessment Vs Initial Plan";
  public static readonly ExitPlan: string = "Exits Plan";
  public static readonly ImpactHighlights: string = "Impact Highlights";
  public static readonly EmployeeName: string = "EmployeeName";
  public static readonly Email: string = "Email";
  public static readonly Designation: string = "Designation";
  public static readonly ContactNo: string = "ContactNo";
  public static readonly Education: string = "Education";
  public static readonly PastExperience: string = "PastExperience";
  public static readonly Region: string = "Region";
  public static readonly Country: string = "Country";
  public static readonly State: string = "State";
  public static readonly City: string = "City";
  public static readonly Customfield: string = "Customfield";
  public static readonly CompanyGroupId: string = "CompanyGroupId";
  public static readonly CompanyLegalName: string = "CompanyLegalName";
  public static readonly InvestmentDate: string = "InvestmentDate";
  public static readonly SustainableDevelopmentGoalsImages = "Sustainable Development Goals Images";
  public static readonly ValuationSummary = "Valuation Summary";
}

export class DealTrackRecordStatic {
  public static readonly Quarter: string = "Quarter";
  public static readonly InvestmentCost: string = "InvestmentCost";
  public static readonly RealizedValue: string = "RealizedValue";
  public static readonly UnrealizedValue: string = "UnrealizedValue";
  public static readonly TotalValue: string = "TotalValue";
  public static readonly Dpi: string = "Dpi";
  public static readonly Rvpi: string = "Rvpi";
  public static readonly GrossMultiple: string = "GrossMultiple";
  public static readonly GrossIRR: string = "GrossIRR";
  public static  readonly InvestmentDate:string="InvestmentDate";
  public static  readonly ValuationDate:string="ValuationDate";
  public static  readonly Status:string="Status";
  public static readonly Year: string = "Year";
  public static readonly Customfield = "Customfield";
}

export class M_Datatypes {
  public static readonly FreeText: number = 1;
  public static readonly CurrencyValue: number=3;
  public static readonly Number: number = 2;
  public static readonly Multiple: number=5;
  public static readonly Percentage: number=4;
  public static readonly Date: number=6;
  public static readonly List: number=7;
}

export class FundTrackRecordStatic {
  public static readonly Quarter: string = "Quarter";
  public static readonly Year: string = "Year";
  public static readonly TotalNumberOfInvestments: string = "TotalNumberOfInvestments";
  public static readonly RealizedInvestments: string = "RealizedInvestments";
  public static readonly UnRealizedInvestments: string = "UnRealizedInvestments";
  public static readonly TotalInvestedCost: string = "TotalInvestedCost";
  public static readonly TotalRealizedValue: string = "TotalRealizedValue";
  public static readonly TotalUnRealizedValue: string = "TotalUnRealizedValue";
  public static readonly TotalValue: string = "TotalValue";
  public static readonly Dpi: string = "Dpi";
  public static  readonly Rvpi:string="Rvpi";
  public static  readonly GrossMultiple:string="GrossMultiple";
  public static  readonly NetMultiple:string="NetMultiple";
  public static  readonly GrossIRR:string="GrossIRR";
  public static  readonly NetIRR:string="NetIRR";
  public static readonly Customfield = "Customfield";
  public static readonly FundSize = "FundSize";
  public static readonly InvestorStake = "InvestorStake";
  public static readonly InvestmentDate = "InvestmentDate";
  public static readonly CurrentExitOwnershipPercent = "CurrentExitOwnershipPercent";
  public static readonly CommitmentAfterAstreaTransfer = "CommitmentAfterAstreaTransfer";
  public static readonly CurrentEquityValueCalculated= "CurrentEquityValueCalculated";
}
export class FundInvestorConstants {
  public static readonly CommitmentDate: string = "CommitmentDate";
  public static readonly InvestorId: string = "InvestorId";
  public static readonly Commitment: string = "Commitment";
  public static readonly Ownership: string = "Ownership";
  public static readonly NetDrawn: string = "NetDrawn";
  public static readonly Recallable: string = "Recallable";
  public static readonly UndrawnCommitment: string = "UndrawnCommitment";
  public static readonly AstreaTransfer: string = "AstreaTransfer";
  public static readonly InvestorStake: string = "InvestorStake";
  public static readonly CommitmentAfterAstreaTransfer: string = "CommitmentAfterAstreaTransfer";
  public static readonly Customfield = "Customfield";
}

export class NumberDecimalConst {
  public static noDecimal: string = '1.0-0';
  public static singleDecimal: string = '1.0-1';
  public static currencyPercentMultiple: string = '1.1-1';
  public static doubleDecimal: string = '1.2-2';
  public static currencyDecimal: string = '1.1-1';
  public static multipleDecimal: string = '1.1-1';
  public static percentDecimal: string = '1.1-1';
}

export class InvestorStatic {
  public static readonly InvestorName: string = "InvestorName";
  public static readonly Website: string = "Website";
  public static readonly TotalCommitment: string = "TotalCommitment";
  public static readonly InvestorTypeId: string = "InvestorTypeId";
  public static readonly Customfield = "Customfield";
  public static readonly BusinessDescription = "BusinessDescription";
  public static readonly Region: string = "Region";
  public static readonly Country: string = "Country";
  public static readonly State: string = "State";
  public static readonly City: string = "City";
  public static readonly Headquarter: string="Headquarter";
}

export class GeoLocationStatic {
  public static readonly Region: string = "Region";
  public static readonly Country: string = "Country";
  public static readonly State: string = "State";
  public static readonly City: string = "City";
  public static readonly Headquarter: string="Headquarter";
}

export class InvestorDashBoardStatic {
  public static readonly InvestedCapital: string = "InvestedCapital";
  public static readonly TotalValue: string = "TotalValue";
  public static readonly RealizedValue: string = "RealizedValue";
  public static readonly UnRealizedValue: string = "UnRealizedValue";
  public static readonly TotalFunds: string="TotalFunds";
  public static readonly PortfolioCompanies: string="PortfolioCompanies";
  public static readonly Sectorwise:string="Sector Wise Investment";
  public static readonly Top10:string="Top 10 Portfolio Companies";
  public static readonly TVPIbyVintageYear:string="TVPI by Vintage Year"; 
}

export class FundPageSectionConstants {
  public static readonly StaticInformation: string = "Fund Details";
  public static readonly FundTerm: string = "Fund Terms";
  public static readonly TrackRecord: string = "Track Record"; 
  public static readonly GeoLocation: string = "Geographic Locations";             
}

export class FundStaticDetailConstants {
  public static readonly FundName: string = "FundName";
  public static readonly FirmName: string = "FirmName";
  public static readonly VintageYear: string = "VintageYear";
  public static readonly Currency: string = "Currency";
  public static readonly Sector: string = "Sector";
  public static readonly Strategy: string = "Strategy"; 
  public static readonly StrategyDescription: string = "StrategyDescription";
  public static readonly AccountType: string = "AccountType";
  public static readonly FundSize: string = "FundSize";
  public static readonly Investors: string = "Investors";
  public static readonly CustomField: string = "Customfield";    

}

export class FundTermFieldConstants {
  public static readonly ManagementFee: string = "ManagementFee";
  public static readonly TargetCommitment: string = "TargetCommitment";
  public static readonly GPCommitment: string = "GPCommitment";
  public static readonly PreferredReturnPercent: string = "PreferredReturnPercent";
  public static readonly CarriedInterestPercent: string = "CarriedInterestPercent";
  public static readonly MaximumExtensionToFundTerm: string = "MaximumExtensionToFundTerm";
  public static readonly MaximumCommitment: string = "MaximumCommitment";
  public static readonly GPCatchupPercent: string = "GPCatchupPercent";
  public static readonly Clawback: string = "Clawback";
  public static readonly ManagementFeeOffset: string = "ManagementFeeOffset";
  public static readonly FundClosingDate: string = "FundClosingDate";
  public static readonly FundTerm: string = "FundTerm";
  public static readonly OrgExpenses: string = "OrgExpenses";    
}
export class ValuationTable{
  public static readonly QuarterMonth: string="Quarter/Month";
  public static readonly CapitalCall:string ="CapitalCall";
  public static readonly CapitalDistribution: string="CapitalDistribution";
  public static readonly FeesAndExpenses: string="FeesAndExpenses";
  public static readonly UnrealizedGainOrLoss: string="UnrealizedGainOrLoss";
  public static readonly ClosingNAV: string="ClosingNAV";
  public static readonly CustomField: string = "Customfield"; 
}
export class InvestorCompanyPerformance{
public static readonly FundName ="FundName";
public static readonly FundSize  ="FundSize";
public static readonly CommitmentAfterAstreaTransfer ="CommitmentAfterAstreaTransfer";
public static readonly InvestorStake ="InvestorStake";
public static readonly InvestmentDate ="InvestmentDate";
public static readonly CurrentExitOwnershipPercent ="CurrentExitOwnershipPercent";
public static readonly CurrentEquityValueCalculated ="CurrentEquityValueCalculated";
public static readonly OriginalInvestedCapital ="OriginalInvestedCapital";
public static readonly OriginalRealizedValue  ="OriginalRealizedValue";
public static readonly OriginalUnrealizedValue ="OriginalUnrealizedValue";
public static readonly OriginalTotalValue ="OriginalTotalValue";
public static readonly InvestmentCost ="InvestmentCost";
public static readonly RealizedValue ="RealizedValue";
public static readonly UnrealizedValue ="UnrealizedValue";
public static readonly TotalValue ="TotalValue";
public static readonly Dpi ="Dpi";
public static readonly Rvpi ="Rvpi";
public static readonly GrossMultiple ="GrossMultiple";
public static readonly GrossIRR ="GrossIRR";
public static readonly  Status="Status";
}    

export class FinancialsSubTabs {
  public static Actual: string = 'Actual';
  public static Budget: string = 'Budget';
  public static Forecast: string = 'Forecast';
  public static IC: string = 'IC';
}
export class TopHoldings{
  public static PortfolioCompany:string="Portfolio Company";
  public static Fund:string="Fund";
  public static Status:string="Status";
  public static TopHoldingByMostInvested:string = "Holdings By Most Invested";
  public static TopHoldingsByCompanyValuation:string = "Holdings By Company Valuation";
  public static TopHoldingsByGrossTvpi:string = "Holdings By Gross TVPI";
}

export class KpiInfo {
  public static Number: string = '#';
  public static Percentage: string = '%';
  public static Currency: string = '$';
  public static Multiple: string = 'x';
  public static Text: string = 'Text';
}

export class ModuleList {
  public static User: string = 'user';
  public static Firm: string = 'firm';
  public static Deal: string = 'deal';
  public static Fund: string = 'fund';
  public static Company: string = 'portfolio company';
  public static CompanyKpi: string = 'company kpi';
  public static Financials: string = 'financials';
  public static ImpactKpi: string = 'impact kpi';
  public static InvestmentKpi: string = 'investment kpi';
  public static MonthlyReport: string = 'monthly report';
  public static OperationalKpi: string = 'operational kpi';
  public static ExchangeRates: string = 'exchange rates';
  public static TradingRecords: string = 'trading records';
  public static CreditKpi: string = 'credit kpi';
  public static ValuationTable: string = 'valuation table';
  public static Adhoc: string = 'adhoc';
  public static Investor: string = 'investor';
  public static FOF: string = 'fund beta';
  public static ESG: string = 'esg';
  public static CapTable: string = 'captable';
  public static CustomTable1: string = 'custom table1';
  public static CustomTable2: string = 'custom table2';
  public static CustomTable3: string = 'custom table3';
  public static CustomTable4: string = 'custom table4';
  public static OtherKPI1: string = 'other kpi1';
  public static OtherKPI2: string = 'other kpi2';
  public static OtherKPI3: string = 'other kpi3';
  public static OtherKPI4: string = 'other kpi4';
  public static OtherKPI5: string = 'other kpi5';
  public static OtherKPI6: string = 'other kpi6';
  public static OtherKPI7: string = 'other kpi7';
  public static OtherKPI8: string = 'other kpi8';
  public static OtherKPI9: string = 'other kpi9';
  public static OtherKPI10: string = 'other kpi10';
}

export class KpiTypes{
  public static Company = {name:"Company", type:"CompanyKPIs"}
  public static TradingRecords= {name:"Trading Records", type:"TradingRecords"} ; 
  public static TradingRecordsBeta= {name:"Trading Records Beta", type:"TradingRecords"} ;
  public static OperationalBeta= {name:"Operational KPI Beta", type:"OperationalKPIs"} ;  
  public static Operational= {name:"Operational", type:"OperationalKPIs"} ; 
  public static Impact= {name:"Impact", type:"ImpactKPIs"} ; 
  public static ProfitLoss= {name:"Profit & Loss", type:"ProfitLoss"} ; 
  public static BalanceSheet= {name:"Balance Sheet", type:"BalanceSheet"} ;  
  public static CashFlow= {name:"Cash Flow", type:"CashFlow"} ; 
  public static Investment= {name:"Investment", type:"InvestmentKPIs"} ; 
  public static InvestmentBeta= {name:"Investment KPI Beta", type:"InvestmentKPIs"} ; 
  public static Credit= {name:"Credit", type:"CreditKPI"} ; 
  public static CustomTable1 = {name:"Custom Table1", type:"CustomTable1"} ; 
  public static CustomTable2 = {name:"Custom Table2", type:"CustomTable2"} ;
  public static CustomTable3 = {name:"Custom Table3", type:"CustomTable3"} ;
  public static CustomTable4 = {name:"Custom Table4", type:"CustomTable4"} ;
  public static OtherKPI1 = {name:"Other KPI1", type:"OtherKPI1"} ;
  public static OtherKPI2 = {name:"Other KPI2", type:"OtherKPI2"} ;
  public static OtherKPI3 = {name:"Other KPI3", type:"OtherKPI3"} ;
  public static OtherKPI4 = {name:"Other KPI4", type:"OtherKPI4"} ;
  public static OtherKPI5 = {name:"Other KPI5", type:"OtherKPI5"} ;
  public static OtherKPI6 = {name:"Other KPI6", type:"OtherKPI6"} ;
  public static OtherKPI7 = {name:"Other KPI7", type:"OtherKPI7"} ;
  public static OtherKPI8 = {name:"Other KPI8", type:"OtherKPI8"} ;
  public static OtherKPI9 = {name:"Other KPI9", type:"OtherKPI9"} ;
  public static OtherKPI10 = {name:"Other KPI10", type:"OtherKPI10"} ;
}

export class KpiModuleAlias{
  public static Company: string = "CompanyKPIs";
  public static FundKpiFirstModuleId: number = 1009;
  public static TradingRecords: string = "TradingRecords"; 
  public static Operational: string = "OperationalKPIs"; 
  public static Impact: string = "ImpactKPIs"; 
  public static Investment: string = "InvestmentKPIs"; 
  public static Credit: string = "CreditKPI"; 
  public static CapitalizationTable: string = "Capitalization Table"; 
  public static Financials: string = "Financials"; 
  public static ProfitAndLoss: string = "ProfitAndLoss"; 
  public static BalanceSheet: string = "BalanceSheet"; 
  public static CashFlow: string = "CashFlow"; 
  public static List: string[] = ["CompanyKPIs", "TradingRecords", "OperationalKPIs", "ImpactKPIs", "InvestmentKPIs", "CreditKPI","Capitalization Table",
  "Financials", "ProfitAndLoss", "BalanceSheet", "CashFlow", "CustomTable1", "CustomTable2", "CustomTable3", "CustomTable4", "CapTable1", "CapTable2", "CapTable3", "CapTable4", "CapTable5",
  "OtherKPI1", "OtherKPI2", "OtherKPI3", "OtherKPI4", "OtherKPI5", "OtherKPI6", "OtherKPI7", "OtherKPI8", "OtherKPI9", "OtherKPI10", "FundFinancials", "FundFinancials1", "FundFinancials2", 
  "FundFinancials3", "FundFinancials4", "FundFinancials5", "FundFinancials6", "FundFinancials7", "FundKeyFinancials1", "FundKeyFinancials2", "FundKeyFinancials3", "FundKeyFinancials4", 
  "FundKeyFinancials5", "FundKeyFinancials6", "FundKeyFinancials7", "FundKeyFinancials8"];
  public static FundKpis = [{name:"FundFinancials", moduleId:1001}, {name:"FundFinancials1", moduleId:1002}, {name:"FundFinancials2", moduleId:1003}, 
    {name:"FundFinancials3", moduleId:1004}, {name:"FundFinancials4", moduleId:1005}, {name:"FundFinancials5", moduleId:1006}, {name:"FundFinancials6", moduleId:1007}, 
    {name:"FundFinancials7", moduleId:1008}, {name:"FundKeyFinancials1", moduleId:1009}, {name:"FundKeyFinancials2", moduleId:1010}, {name:"FundKeyFinancials3", moduleId:1011}, 
    {name:"FundKeyFinancials4", moduleId:1012}, {name:"FundKeyFinancials5", moduleId:1013}, {name:"FundKeyFinancials6", moduleId:1014}, {name:"FundKeyFinancials7", moduleId:1015}, 
    {name:"FundKeyFinancials8", moduleId:1016}];
  public static CustomTable1: string = "CustomTable1";
  public static CustomTable2: string = "CustomTable2";
  public static CustomTable3: string = "CustomTable3";
  public static CustomTable4: string = "CustomTable4";
  public static CapTable1: string = "CapTable1";
  public static CapTable2: string = "CapTable2";
  public static CapTable3: string = "CapTable3";
  public static CapTable4: string = "CapTable4";
  public static CapTable5: string = "CapTable5";
  public static OtherKPI1: string = "OtherKPI1";
  public static OtherKPI2: string = "OtherKPI2";
  public static OtherKPI3: string = "OtherKPI3";
  public static OtherKPI4: string = "OtherKPI4";
  public static OtherKPI5: string = "OtherKPI5";
  public static OtherKPI6: string = "OtherKPI6";
  public static OtherKPI7: string = "OtherKPI7";
  public static OtherKPI8: string = "OtherKPI8";
  public static OtherKPI9: string = "OtherKPI9";
  public static OtherKPI10: string = "OtherKPI10";
  public static FundFinancials: string = "FundFinancials";
  public static FundFinancials1: string = "FundFinancials1";
  public static FundFinancials2: string = "FundFinancials2";
  public static FundFinancials3: string = "FundFinancials3";
  public static FundFinancials4: string = "FundFinancials4";
  public static FundFinancials5: string = "FundFinancials5";
  public static FundFinancials6: string = "FundFinancials6";
  public static FundFinancials7: string = "FundFinancials7";
  public static FundKeyFinancials1: string = "FundKeyFinancials1";
  public static FundKeyFinancials2: string = "FundKeyFinancials2";
  public static FundKeyFinancials3: string = "FundKeyFinancials3";
  public static FundKeyFinancials4: string = "FundKeyFinancials4";
  public static FundKeyFinancials5: string = "FundKeyFinancials5";
  public static FundKeyFinancials6: string = "FundKeyFinancials6";
  public static FundKeyFinancials7: string = "FundKeyFinancials7";
  public static FundKeyFinancials8: string = "FundKeyFinancials8";
}

export class PeriodTypeFilterOptions{
  public static Monthly: string = "Monthly";
  public static Quarterly: string = "Quarterly" ;
  public static Annual: string = "Annual";
  public static isMonthly: string = "isMonthly";
  public static isQuarterly: string = "isQuarterly" ;
  public static isAnnually: string = "isAnnually";
  public static Reset: string = "Reset";
}

export class PeriodType{
  public static filterOptions: any[] = [
    { field: PeriodTypeFilterOptions.Monthly, key: false},
    { field: PeriodTypeFilterOptions.Quarterly, key: false },
    { field: PeriodTypeFilterOptions.Annual, key: false }
  ];
    }
export class Constants{
  public static  DealTrackRecord:string="Portfolio Company Fund Holding Details";
  public static  InvestmentProfessionals:string="Investment Professionals";
  public static  HeaderSuccessMessage:string="Company Equity Calculation Details headers successfully changed";
  public static  ContentTitle:string="Values to be subtracted should be entered as negative value\nE.g. -450000";
}
export class ValuationConstants{
  public static FinalValuationNote :string="Only one valuation methodology will be considered as final. Same will be visible on Portfolio Company page.";
  public static DisableDefaultMethodologyNote :string="DEFAULTMETHODOLOGY is current final methodology. Please unselect previous choice to set this as final methodology.";
}
export class FileUploadStatus{
  public static  FOF:string="Fund of Fund";
  public static  ESG:string="ESG";
  public static  errorMessage:string="errorMessage";
  public static  UploadinProgress:string="Upload in progress";
  public static  UploadSuccessful:string="Upload successful"; 
  public static  UploadFailed:string="Upload failed";
  public static  UploadCancelled:string="Upload Cancelled";
  public static  FileUploadProcessImage:string="assets/dist/images/file-upload-process.svg";
  public static  FileUploadSuccessImage:string="assets/dist/images/file-upload-success.svg";
  public static  FileUploadErrorCountImage:string="assets/dist/images/error-count.svg";
  public static  FileUploadFailedImage:string="assets/dist/images/file-upload-failed.svg";
  public static  FileUploadCrossGreyImage:string="assets/dist/images/CrossGrey.svg";
  public static  FileUploadNotificIconImage:string= "assets/dist/images/NotificIcon.svg";
  public static  FileUploadHeaderName:string="headerName";
  public static  FileUploadBulkMessage:string="bulkMessage";
  public static  FileUploadInvokeFileStatusByUserId:string="InvokeFileStatusByUserId";
  public static  FileUploadFileStatusUpdated:string="FileStatusUpdated";
  public static  FileUploadNotification:string="file-upload-notification";
  public static  FileUploadFileuploadStatus:string="/fileuploadstatus";
  public static  FileUploadDummyRoute:string="/dummy";
  public static  EmailNotificationToaster:string="Your file is getting ready! We will share a link through the email </br> once your file is ready for download";
  public static  FileNotdownloadedMessage:string="File not downloaded";

  
}
export class BulkuploadConstants{
  public static ValidateExcelFormat:string="Please upload excel formats only";
  public static validateSupportingFileFormat:string="File format '.exe' is not supported";
  public static error:string="error";
  public static ValidateEmptyFile:string="Please select atleast one file to upload";
  public static FileSize:string="fileSize";
  public static FileName:string="FileName";
  public static FormFile:string="formFile";
  public static supportingDocuments:string="supportingDocuments";
  public static Template:string="Template";
  public static ModuleName:string="ModuleName";
  public static SubPageId:string="SubPageId";
  public static CompanyId:string="CompanyId";
  public static Comments:string="Comments";
  public static exe:string="exe";
  public static fileExtensions: string[] = ["xlsx", "xls"];
  public static InvalidateDataorSheet:string="One or more records in the file has invalid data or sheet(s) may be empty. Please upload the file again after correction.";
  public static ErrorFoundSelectedFile:string="Errors found in selected file";
  public static ToasterMessagePossition:string="toast-center-center";
  public static FileUploadedSuccessfully:string="File uploaded successfully";
  public static ok:string="ok";
  public static MonthlyReportModule:string="monthly report";
  public static MonthlyReport:string="Monthly Report";
}

export class WorkflowPcCommentary{
  public static readonly LPReport="LP Report";
  public static readonly SuccessCommentary="Portfolio company commentary details entry updated successfully";
  public static readonly FailedCommentary="Portfolio company commentary details entry updated failed";
  public static readonly SignificantEventsSection="SignificantEventsSection";
  public static readonly AssessmentSection="AssessmentSection";
  public static readonly ExitPlansSection="ExitPlansSection";
  public static readonly ImpactSection="ImpactSection";
  public static readonly Customfield='Customfield';
}
export const decimalDigitOptions = [
  { unitType: DecimalDigitEnum.One, value: 1 },
  { unitType: DecimalDigitEnum.Two, value: 2 },
  { unitType: DecimalDigitEnum.Three, value: 3 },
  { unitType: DecimalDigitEnum.Four, value: 4 },
  { unitType: DecimalDigitEnum.Five, value: 5 },
  { unitType: DecimalDigitEnum.Six, value: 6 }
];
export class OperationalKPIConstants{
  public static readonly CELL_LOGS_MESSAGE="Click to view this cell logs";
  public static readonly EntryUpdatedSuccessfully="Entry updated successfully";
  public static readonly ToastCenterCenter="toast-center-center";
  public static readonly AuditLogNotSupport="Audit log not supported for headers and formula values";
  public static readonly InternalServerError="InternalServerError";
  public static readonly originalKPI="originalKPI";
  public static readonly Q1="Q1";
  public static readonly Q2="Q2";
  public static readonly Q3="Q3";
  public static readonly Q4="Q4";
  public static readonly KPI="KPI";
  public static readonly KPIInfo="KPI Info";
  public static readonly Text="Text";
  public static readonly X="x";
  public static readonly Percentage="%";
  public static readonly DateRange="Date Range";
  public static readonly AuditLogRoute: string = "/audit-logs";
  public static readonly PageConfigRoute: string = "/page-config";
}
export class CellEditConstants{
  public static ValidateSupportingFileFormat:string="File format '.exe' is not supported";
  public static ToasterMessagePosition:string="toast-center-center";
  public static EXE:string="exe";
  public static UpdatedSupportingDocuments:string="UpdatedSupportingDocuments";
  public static Manual="Manual";
  public static ExtensionError:string="Could not determine file extension.";
  public static readonly KPIInfoSpace="KPI Info";
  public static readonly KPIInfo="kpiInfo"; 
  public static readonly ImpactKpiInfo="KPI Info";
  public static readonly Kpi="Kpi";
  public static readonly TextInfo="Text";
  public static readonly CapTable="CapTable";
  public static readonly ImpactKPIs="ImpactKPIs";
  public static readonly CompanyKPIs="CompanyKPIs";
  public static readonly OperationalKPIs="OperationalKPIs";
  public static readonly CapTableActual=4;
  public static readonly CapTableActualYTD=14;
  public static readonly CapTableActualLTM=16;
  public static readonly CapTableLTM="LTM";
  public static readonly CapTableYTD="YTD";
  public static readonly CapTableIsOverrule="IsOverrule";
  public static readonly ValueTypeId="ValueTypeId";
  public static readonly kpiId="kpiId";
  public static readonly KpiInfo="KpiInfo";
  public static readonly InvestmentKPIs="InvestmentKPIs";
}
export class EsgConstants{
  public static readonly ToastCenterCenter="toast-center-center";
  public static readonly EsgAuditUrl="/esg-audit-logs";
  public static readonly EsgAuditLocalStorage="esg-auditLogData";
}
export class InvestmentKpiConstants{
  public static readonly InvestmentKpiAuditUrl="/audit-logs";
  public static readonly InvestmentKpiAuditLocalStorage="investment-auditLogData";
}

export class CreditKpiConstants{
  public static readonly CreditKpiAuditUrl="/audit-logs";
  public static readonly CreditKpiAuditLocalStorage="credit-auditLogData";
  public static Actual: string = 'Actual';
}

export enum adjustmentType {
  AtPar = 100,
  Discount = 101,
  Premium = 102
}

export const ValuationAttributeTypeList = ['Estimate', 'LTM', 'NTM', 'Actual'];

export const adjustmentTypes = [
  {
    name: "At Par",
    id: adjustmentType.AtPar,
  },
  {
    name: "Discount",
    id: adjustmentType.Discount,
  },
  {
    name: "Premium",
    id: adjustmentType.Premium,
  }
];

// other constants...
export class ImpactKPIConstants{
  public static readonly CELL_LOGS_MESSAGE="Click to view this cell logs";
  public static readonly EntryUpdatedSuccessfully="Entry updated successfully";
  public static readonly ToastCenterCenter="toast-center-center";
  public static readonly AuditLogNotSupport="Audit log not supported for headers and formula values";
  public static readonly InternalServerError="InternalServerError";
  public static readonly originalKPI="originalKPI";
  public static readonly Q1="Q1";
  public static readonly Q2="Q2";
  public static readonly Q3="Q3";
  public static readonly Q4="Q4";
  public static readonly KPI="KPI";
  public static readonly KPIInfo="KPI Info";
  public static readonly Text="Text";
  public static readonly X="x";
  public static readonly Percentage="%";
  public static readonly DateRange="Date Range";
  public static readonly IMPACT_KPI: string = "Impact Kpis";
  public static readonly AuditLogRoute: string = "/audit-logs";
  public static readonly Actual: string = "Actual";
  public static readonly Monthly: string = "Monthly";
  public static readonly Quarterly: string = "Quarterly";
  public static readonly ImpactMenuTrigger: string = 'impactMenuTrigger';
  public static readonly menu: string = 'menu';
  public static readonly dtView: string = 'dt';
  public static readonly KpiId: string = "KpiId";
  public static readonly MappingId: string = "MappingId";
  public static readonly ImpactKPI: string = "ImpactKPI.KPI";
  public static readonly Year: string = "year";
  public static readonly Month: string = "month";
  public static readonly Error: string = "error";
  public static readonly ok: string = "ok";
}
export class FinancialsValueTypes {
  public static LTM: string = 'LTM';
  public static YTD: string = 'YTD';
  public static IC: string = 'IC';
}
export class KendoConstants{ 
  public static asc: string = 'asc';
  public static desc: string = 'desc';
  public static logic: string = 'logic';
  public static or: string = 'or';
  public static filters: string = 'filters';
  public static keyup: string = 'keyup';
  public static targetValue: string = '$event.target.value';
  public static contains: string = 'contains';
  public static success: string = 'success';
  public static warning: string = 'warning';
  public static error: string = 'error';
  public static info: string = 'info';
}

export class ToasterMessageConstants {
  public static getNotificationConfig(content: string, type: string): any {
    let result: NotificationSettings =  {
      content: `<ng-template #template>
      <div>${content}</div>
    </ng-template>`,
      hideAfter: 8000,
      position: { horizontal: "center", vertical: "top" },
      animation: { type: "fade", duration: 400 },
      type: { style: 'warning', icon: true },
      closable: true,

    };
    return result;
  }
  
} 
export const REQUEST_CONFIG_COLUMNS = [
  {
    field: 'requestName',
    header: 'Request Name',
  },
    {
      field: 'companies',
      header: 'Companies'
    },
    {
      field: 'funds',
      header: 'Funds'
    },
    {
      field: 'groups',
      header: 'Groups'
    },
    {
      field: 'createdBy',
      header: 'Created By'
    },
    
    {
      field: 'createdDate',
      header: 'Creation Date'
    },
    {
      field: 'type',
      header: 'Type'
    },
   {
      field: 'status',
      header: 'Status'
    }
  ];
  export const GEOGRAPHIC_PROPERTIES = {
    REGION_CHANGE: ["state", "city", "country"],
    COUNTRY_CHANGE: ["state", "city"],
    STATE_CHANGE: ["city"],
  };

  export const LOCATION_MODEL_LISTS = {
    REGION_CHANGE: ["countryList", "stateList", "cityList"],
    COUNTRY_CHANGE: ["stateList", "cityList"],
    STATE_CHANGE: ["cityList"],
  };
  export class InvestorPermissionConstants {
    public static readonly InvestorSubFeature = {
      InvestorInformation: "InvestorInformation",
      GeographicalLocations: "GeographicalLocations",
      InvestedFunds: "InvestedFunds",
      Dashboard: "Dashboard",
      CompanyPerformance: "CompanyPerformance",
      ValuationData: "ValuationData",
      InvestorTrackRecord: "InvestorTrackRecord"
    };
  }
  export class FundPermissionConstants {
    public static readonly FundSubFeature = {
      FundDetails: "FundDetails",
      GeographicLocations: "GeographicalLocations",
      FundTerms: "FundTerms",
      TrackRecord: "TrackRecord",
      InvestedFunds: "InvestedFunds"
    };
  }
  export const PermissionActions = {
    CAN_VIEW:'canView',
    CAN_EDIT:'canEdit',
    CAN_EXPORT:'canExport',
    CAN_ADD:'canAdd',
    CAN_IMPORT:'canImport'

  };

  export const LpTemplateConstants = {
    COMPANY_NAME:'CompanyName',
    COMPANY_LOGO:'CompanyLogo',
    STATIC_INFORMATION:'Static Information',
    BUSINESS_DESCRIPTION:'BusinessDescription',
    GEOGRAPHIC_LOCATIONS:'Geographic Locations',
    INVESTMENT_PROFESSIONALS:'Investment Professionals',
    PeriodSelectionWarning:"Only single last & next selections (if applicable) are allowed under each scenario and frequency.",
    COMMENTARY:'Commentary',
    INVESTMENT_COLUMN:1060,
    INVESTMENT_KPI_ID:4,
    SDGLOGO:'SDG Logo',
  };
  export const REPORT_URLS = {
    "LP Report": '/lp-report-configuration',
    "Fund Report": '/fund-report-configuration',
    "Internal Report": '/internal-report-configuration',
    "Fund of Fund Report": '/consolidated-report-configuration',
    "Growth Report": '/growth-report'
  };
export const ACTUAL = 4;
export const ACTUAL_LTM = 14;
export const ACTUAL_YTD = 16;
export const LAST_1Q_TEXT = "Last 1Q";
export const ImageExtensions: string[] = ['.jpg', '.jpeg','.png','.gif','.bmp','.tiff','.tif','.svg','.webp','.ico','.heic','.heif','.jfif','.pjpeg','.pjp','.avif'];
export const PortfolioCompanyDetailConstants = {
  STATIC_INFORMATION: 'Static Information',
  KEY_PERFORMANCE_INDICATOR: 'Key Performance Indicator',
  COMPANY_FINANCIALS: 'Company Financials',
  COMMENTARY: 'Commentary',
  CAP_TABLE: 'CapTable',
  OTHER_KPIS: 'Other KPIs',
  SUSTAINABLE_DEVELOPMENT_GOALS_IMAGES: 'Sustainable Development Goals Images',
  INVESTMENT_PROFESSIONALS: 'Investment Professionals',
  GEOGRAPHIC_LOCATIONS: 'Geographic Locations',
  VALUATION_SUMMARY: 'Valuation Summary',
  Documents: 'Documents',
  PORTFOLIO_COMPANY_FUND_HOLDING_DETAILS: 'Portfolio Company Fund Holding Details'
};
export class DataIngestionConstants {
  static readonly MAX_FILES_MESSAGE = (count: number) => {
    const formattedCount = count < 10 ? `0${count}` : count;
    const fileText = count === 1 ? 'file' : 'files';
    return `Only ${formattedCount} ${fileText} can be uploaded at a single time. Please reupload.`;
};
  static readonly EMPTY_TITLE = '';
  static readonly ToasterMessagePosition="toast-center-center";
  static readonly DUPLICATE_FILE_ERROR = 'Duplicate file uploaded.';
  static readonly INVALID_FORMAT_ERROR = 'File format is not ".pdf."';
  static readonly FILE_SIZE_ERROR = (maxSizeMB: number) => `File size exceeds ${maxSizeMB}MB.`;
  static readonly SIZE_AND_FORMAT_ERROR = (maxSizeMB: number) => 
      `File size exceeds ${maxSizeMB}MB and file format is not ".pdf".`;
  static readonly QUARTER_SELECTION = 'Select Quarter';
  static readonly MONTH_SELECTION = 'Select Month';
  static readonly YEAR_SELECTION = 'Select Year';
  static readonly DEFAULT_PERIOD_SELECTION = 'Select Period';
  static readonly VALID="valid";
  static readonly INVALID="invalid";
  static readonly PDF="pdf";
  static readonly FILES_NOT_UPLOADED = (count: number) => {
      const formattedCount = count < 10 ? `0${count}` : count;
      return `${formattedCount} files were not uploaded!`;
    };
    static readonly API_ERROR="API error: The system was unable to scan all the pages. Kindly enter the pages manually.";
    static readonly SpecificKPI="Specific KPI";
    static readonly AsIsExtraction="As Is Extraction";
}
export const TITLES = {
  ADD_CLO: 'Add Collateral Loan Obligation',
  UPDATE_CLO: 'Update Collateral Loan Obligation'
};

export const CLO_TABLES = {
  Key_KPIs_US: 'Key_KPIs_US',
  Key_KPIs_EU: 'Key_KPIs_EU'
}

//CLO Constants
export class CLOConstants{
  public static readonly Delete_Confirmation_Body_1: string = "Are you sure you want to delete";
  public static readonly Delete_Confirmation_Body_2: string = "This action cannot be undone.";
  public static readonly Delete_Note: string = "Note";
  public static readonly Delete_Note_1: string = "Deleting the";
  public static readonly Delete_Note_2: string = "will permanently erase all";
  public static readonly Delete_Note_3: string = "associated data";
  public static readonly Modal_Title: string = "Delete Collateral Loan Obligation (CLO)";
  public static readonly Success_message: string = "You have successfully Deleted";
  public static readonly Error_message: string = "Error while deleting";
  public static readonly Delete_Table_Note_1: string = "Warning : Irreversible Action";
  public static readonly Delete_Table_Note_2: string = "All data contained within this table will be permanently deleted.";
}

// CLO service constants
export class CLOListServiceConstants{
  public static GetCLOURL:string="api/v1/CLO/clo-details/get/";
  public static SaveCLOURL:string="api/v1/CLO/clo-details/add";
  public static DeleteCLOURL:string="api/v1/CLO/clo-details/delete/";
  public static DeleteCLOTableURL:string="api/v1/CLO/clo-details/deleteTable/";
}

export class TOASTER_MSG{
  public static readonly NOT_FOUND: string = "Investment Company data not found";
  public static readonly CLO_NOT_FOUND: string = "Collateral Loan Obligation (CLO) data not found";
  public static readonly POS_CENTER:string="toast-center-center";
}

export class CLOPermissionConstants {
  public static readonly CLOSubFeature = {
    CLOSummary: 'CLOSummary',
    CapitalStructure: 'Capital Structure',
    Collateral: 'Collateral',
    KeyKPI: 'Key KPI',
    OvercollateralisationTest: 'Overcollateralisation Test',
    CollateralQualityTest: 'Collateral Quality Test',
    CLOVersusCLOSector: 'CLO Versus CLO Sector',
    CLODistributionsToDate: 'CLO Distributions To Date',
    CompanyFacts: 'CompanyFacts',
    InvestmentSummary: 'InvestmentSummary',
    GLIPortfolioComposition: 'GLI Portfolio Composition',
    AggregateCloMetric: 'Aggregate CLO Metrics',
    NAVDistribution: 'NAV & Distribution',
    PEPerformanceIndicators: 'PE Performance Indicators',
    ReturnAnalysis: 'Return Analysis',
    ReturnComposition: 'Return Composition',
    CurrencyExposure: 'Currency Exposure',
    GLICommentry: 'GLICommentry',
    MarketCommentry: 'MarketCommentry',
    InvestmentCompany:'Investment Company',
    KPIHistory : 'KPI History',
    Summary:'Summary',
    Leverage: 'Leverage',

  };
}

export class CLOPageConfig{
  public static readonly MSG_SUCCESS: string = "Page Configuration saved successfully";
  public static readonly MSG_Error: string = "Error occured while saving";
  public static readonly MSG_Empty: string = "Empty fields are not allowed";
  public static readonly POSITION_CENTER: string = "toast-center-center";
}
export abstract class FeatureTableMapping {
  public static readonly FEATURE_TABLE_MAP = {
    CLOSummary: { FeatureId: 82, TableId: [23]},
    CapitalStructure: { FeatureId: 83, TableId: [9] },
    Collateral: { FeatureId: 84, TableId: [16]},
    KeyKPI: { FeatureId: 85, TableId: [10, 11] },
    OvercollateralisationTest: { FeatureId: 86, TableId: [17]},
    CollateralQualityTest: { FeatureId: 87, TableId: [18] },
    CLOVersusCLOSector: { FeatureId: 88, TableId: [15] },
    CLODistributionsToDate: { FeatureId: 89, TableId: [19]},
    CompanyFacts: { FeatureId: 71, TableId: [21]},
    InvestmentSummary: { FeatureId: 72, TableId: [22]  },
    GLIPortfolioComposition: { FeatureId: 73, TableId: [5]},
    AggregateCloMetric: { FeatureId: 74, TableId: [7, 8] },
    NAVDistribution: { FeatureId: 75, TableId: [1]},
    PEPerformanceIndicators: { FeatureId: 76, TableId: [2]},
    ReturnAnalysis: { FeatureId: 77, TableId: [3]},
    ReturnComposition: { FeatureId: 78, TableId: [6]},
    CurrencyExposure: { FeatureId: 79, TableId: [4]},
    GLICommentry: { FeatureId: 80, TableId: [24] },
    MarketCommentry: { FeatureId: 81, TableId: [25] },
    KPIHistory: { FeatureId: 90, TableId: [12, 13] },
    Summary: { FeatureId: 91, TableId: [14] },
    Leverage: { FeatureId: 92, TableId: [20]}
  };
  public static readonly TABLES_NAME = {
    Investment_Summary:[22],
    Company_Facts:[21],
    KPI_Summary: [14],
    GLI_Portfolio_Composition:[5],
    NAV_DISTRIBUTION:[1],
    CLO_Distributions_To_Date:[19],
    Aggregate_CLO_Metrics_EU:[7],
    Aggregate_CLO_Metrics_US:[8],
    Capital_Structure:[9],
  }
  public static readonly TABS_NAME = {
    Key_KPI_History:[15],    
    Commentaries:[3],
    PE_Performance_Indicators:[5],
  }
  public static readonly EDIT_DOMICILE_TABLES_NAME = {
    Aggregate_CLO_Metrics:[8],
    Key_KPIs:[10],
    KPI_History:[13]
  }
  public static readonly TOBE_CHANGE_DOMICILE_TABLES_NAME = {
    Aggregate_CLO_Metrics:[7],
    Key_KPIs:[11],
    KPI_History:[12]
  }
}


export class DashboardConfigurationConstants{
  public static DashboardTrackerTabName = "Dashboard Tracker";
  public static DashboardConfigurationTab = 'Dashboard Configuration';
  public static ManageTrackerFieldsTab = 'Manage Tracker Fields';
  public static DeletedColumnTab = 'Deleted Column';
  public static StatusFilterTab = 'Status Filter';
  public static fieldTypesOptions = [
    { text: 'Time Series', value: 1 },
    { text: 'Data', value: 2 }
  ];
  public static dataTypesOptions = [
    { text: 'Text', value: 1 },
    { text: 'Number', value: 2 },
    { text: 'Date', value: 3 },
    { text: 'Dropdown', value: 4 },
  ];
  public static trackingFrequencyOptions = [
    { text: 'Monthly', value: 1 },
    { text: 'Quarterly', value: 2 },
    { text: 'Annual', value: 3 },
  ];
  
}

export const DataIngestionModuleConstants = [
  {
    moduleId: 7,
    moduleName:"Income Statement",
    items: "",
    key:"ProfitAndLoss",
    alias:"Income Statement"
  },
  {
    moduleId: 8,
    moduleName:"Balance Sheet",
    items: "",
    alias:"Balance Sheet"
  },
  {
    moduleId: 9,
    moduleName:"Cash Flow",
    items: "",
    alias:"Cash Flow"
  }
];
export const SUBPAGE_FIELD_NAMES = {
  SPECIFIC_KPI_EXTRACTION: 'Specific Kpi Extraction',
  ASIS_EXTRACTION: 'AsIs Extraction'
};
export const DATA_INGESTION_SUBPAGE_ID = 50;