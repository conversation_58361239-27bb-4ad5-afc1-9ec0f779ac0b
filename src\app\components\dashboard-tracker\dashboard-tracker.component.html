<app-loader-component *ngIf="isLoading"></app-loader-component>

<kendo-grid
  id="dashboardTrackerTable"
  class="dashboard-tracker-table"
  [ngClass]="passedClass"
  [data]="gridData"
  [scrollable]="'scrollable'"
  [sortable]="true"
>

  <kendo-grid-column field="fundName" title="Fund" [width]="moreColumn ? '' : '400'">
    <ng-template *ngIf="isDashboardConfigurationTab" kendoGridHeaderTemplate let-dataItem>
      <input type="checkbox" kendoCheckBox />
      <span class="Body-R header-title ml-3">Fund</span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column title="Portfolio Company" class="d-flex">
    <ng-template *ngIf="isDashboardConfigurationTab" kendoGridHeaderTemplate let-dataItem>
      <input type="checkbox" kendoCheckBox />
      <span class="Body-R header-title ml-3">Portfolio Company</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
          <img
            [src]="dataItem.companyLogo"
            alt="logo"
            *ngIf="dataItem.companyLogo"
            class="company-logo mr-2 p-1"
          />
          <span *ngIf="!dataItem.companyLogo" class="text-logo mr-2">{{dataItem.portfolioCompanyName.slice(0,1)}}</span>
      <span class="d-flex justify-content-center align-items-center">{{ dataItem.portfolioCompanyName }}</span>
    </ng-template>
  </kendo-grid-column>
  <!-- Add new column at the end for plus icon button -->
  <kendo-grid-column title="" width="60" *ngIf="isDashboardConfigurationTab">
    <ng-template kendoGridHeaderTemplate>
      <app-kendo-button (click)="navigateToDashboardConfig()" name="dashboard-tracker-setting" type="Secondary" icon="plus">
      </app-kendo-button>
    </ng-template>
  </kendo-grid-column>

<!-- default screen if grid data is empty -->
  <ng-template kendoGridNoRecordsTemplate>
    <div class="text-center py-5 mt-5" *ngIf="gridData.length === 0">
      <img
        src="assets/dist/images/Illustrations.svg"
        alt="No data"
        class="mb-3"
      />
      <p class="mb-0 Body-R content-secondary">No Data Found</p>
    </div>
  </ng-template>
</kendo-grid>