import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { DashboardTrackerComponent } from './dashboard-tracker.component';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

class MockDashboardTrackerService {
  getDashboardData() {
    return of([{ id: 1, name: 'Test' }]);
  }
}

fdescribe('DashboardTrackerComponent', () => {
  let component: DashboardTrackerComponent;
  let fixture: ComponentFixture<DashboardTrackerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DashboardTrackerComponent],
      providers: [
        { provide: DashboardTrackerService, useClass: MockDashboardTrackerService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardTrackerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set gridData and isLoading on init', () => {
    expect(component.gridData.length).toBe(1);
    expect(component.isLoading).toBe(false);
  });
});
