import { Component } from '@angular/core';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-manage-tracker-fields',
  templateUrl: './manage-tracker-fields.component.html',
  styleUrls: ['./manage-tracker-fields.component.scss']
})
export class ManageTrackerFieldsComponent {
  public fieldTypesOptions: { text: string, value: number }[] = DashboardConfigurationConstants.fieldTypesOptions;
  public dataTypesOptions: { text: string, value: number }[] = DashboardConfigurationConstants.dataTypesOptions;
  public trackingFrequencyOptions: { text: string, value: number }[] = DashboardConfigurationConstants.trackingFrequencyOptions;

  selectedFieldType: number | null = null;
  selectedDataType: number | null = null;
  selectedTrackingFrequency: object = DashboardConfigurationConstants.trackingFrequencyOptions[0];
  namePattern: string | null = null;

  isCreateEnabled(): boolean {
     return this.selectedFieldType !== null && this.selectedFieldType !== undefined;
  }

  trackingPeriodLabel(): string {
    const periodLabels: { [key: number]: string } = {
      1: 'Month',
      2: 'Quarter',
      3: 'Year'
    };
    return periodLabels[this.selectedTrackingFrequency['value']] || '';
  }
}
