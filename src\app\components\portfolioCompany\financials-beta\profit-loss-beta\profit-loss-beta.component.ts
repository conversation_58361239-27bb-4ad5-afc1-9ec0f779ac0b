import { Component, Input,EventEmitter,Output,ViewChild,OnChanges } from '@angular/core';
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { ActivatedRoute } from '@angular/router';
import { ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService, PeriodType } from 'src/app/services/miscellaneous.service';
import { ProfitLossService } from 'src/app/services/profit-loss.service';
import { Table } from 'primeng/table';
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { NumberDecimalConst, GlobalConstants, KpiTypesConstants } from "src/app/common/constants";
import { HttpResponse } from '@angular/common/http';
import { KpiConfig } from 'src/app/components/portfolioCompany/models/Kpi-Configuartion.model';
import { ModuleCompanyModel, TableHeader, MappedDocuments, Audit } from 'src/app/components/file-uploads/kpi-cell-edit/kpiValueModel';
import { ToastrService } from 'ngx-toastr';
import { AuditService } from "src/app/services/audit.service";
import { extractDateComponents } from 'src/app/components/file-uploads/kpi-cell-edit/cell-edit-utils';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { isNil } from 'src/app/utils/utils';
import { DatePipe } from '@angular/common';
import { getConversionErrorMessage } from 'src/app/utils/utils';
@Component({
  selector: "app-profit-loss-beta",
  templateUrl: "./profit-loss-beta.component.html",
  styleUrls: ["./profit-loss-beta.component.scss"],
})
export class ProfitLossBetaComponent implements OnChanges {
  infoUpdate:boolean = false;
  NumberDecimalConst = NumberDecimalConst;
  kpiModuleId = KPIModulesEnum.ProfitAndLoss;
  profitAndLossSearchFilter: any;
  @Input() model;
  @Input() tabName: string = null;
  @Input() valueType = "";
  @Input() isErrorLog: boolean = false;
  @Input() periodType: number = 0;
  @Input() sectionId: number = 0;
  @Input() selectedCurrency = "";
  @Input() isDownload: boolean = false;
  subtabName: string = "Actual";
  portfolioCompanyID: any;
  currencyCode = "";
  id: any;
  isexport: boolean = false;
  isLoader: boolean = false;
  currencyRateSource = "";
  @Output() isDownloading: EventEmitter<any> = new EventEmitter();
  @Output() changeOptionType: EventEmitter<any> = new EventEmitter();
  @Output() onChangeValueType: EventEmitter<any> = new EventEmitter();
  profitAndLossValuesMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  frozenHeader: any = [{ field: "Kpi", header: "KPI" }];
  tableColumns = [];
  tableFrozenColumns = [];
  tableResult = [];
  tableResultClone = [];
  @Input() isFilter: boolean = false;
  @ViewChild("dt") dt: Table | undefined;
  isPageLoad: boolean = true;
  auditLogList: any = [];
  @Input() loadData: boolean = false;
  @Input() pageConfigData: KpiConfig;
  currentPeriodType: number = undefined;
  currentValueType: string = undefined;
  @Input() isYtd: boolean = false;
  @Input() isLtm: boolean = false;
  @Input() valueTypeString: string;
  isYtdPageLoad: boolean = true;
  isLtmPageLoad: boolean = true;
  @Input() profitLossPermission: any = [];
  plEditPermission: boolean = false;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
  auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
  constructor(
    private _avRoute: ActivatedRoute,
    private identityService: OidcAuthService,
    private miscService: MiscellaneousService,
    private profitLossService: ProfitLossService,
    public toasterService: ToastrService,
    private auditService: AuditService,
    private datePipe: DatePipe
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
  }
  ngOnChanges(changes: any) {
    if (this.pageConfigData != undefined) {
      if (
        changes["valueType"] ||
        changes["periodType"] ||
        changes["isFilter"] ||
        changes["loadData"] ||
        changes["isYtd"] ||
        changes["isLtm"]
      ) {
        this.plEditPermission = this.profitLossPermission?.some(
          (x) => x.canEdit
        );
        this.isPageLoad = this.isPageLoad ? true : false;
        this.isYtdPageLoad = this.isYtdPageLoad ? true : false;
        this.isLtmPageLoad = this.isLtmPageLoad ? true : false;
        if (
          changes["isYtd"] ||
          changes["isLtm"] ||
          (this.loadData &&
            changes["periodType"] &&
            (this.currentPeriodType != this.periodType ||
              this.currentValueType != this.valueType)) ||
          !changes["periodType"]
        )
          this.getProfitLossData();
      }
      if (changes["isDownload"] && this.isDownload) {
        this.isexport = true;
        this.exportProfitAndLossData();
      }
    }
  }
  getProfitLossData() {
    let periodType =
      this.model.periodType != undefined ? this.model.periodType.type : null;
    let searchFilter = {
      sortOrder: null,
      periodType: periodType,
      startPeriod:
        this.model.startPeriod != undefined ? this.model.startPeriod[0] : null,
      endPeriod:
        this.model.startPeriod != undefined ? this.model.startPeriod[1] : null,
    };
    this.getProfitLossValues(
      null,
      searchFilter,
      this.model.portfolioCompany.portfolioCompanyID,
      this.currencyCode
    );
    this.profitAndLossSearchFilter = searchFilter;
  }
  getProfitLossValues(
    event: any,
    searchFilter: any,
    companyId: any,
    currencyCode: any
  ) {
    this.isLoader = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "displayOrder",
        multiSortMeta: this.profitAndLossValuesMultiSortMeta,
        sortOrder: -1,
      };
    }
    if (searchFilter == null) {
      searchFilter = {
        sortOrder: null,
        periodType: this.model.periodType.type,
      };
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            this.model.startPeriod.getFullYear(),
            this.model.startPeriod.getMonth(),
            this.model.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            this.model.endPeriod.getFullYear(),
            this.model.endPeriod.getMonth(),
            this.model.endPeriod.getDate()
          )
        );
      }
    } else {
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            searchFilter.startPeriod.getFullYear(),
            searchFilter.startPeriod.getMonth(),
            searchFilter.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            searchFilter.endPeriod.getFullYear(),
            searchFilter.endPeriod.getMonth(),
            searchFilter.endPeriod.getDate()
          )
        );
      }
    }
    let model = this.getDataModel(event, searchFilter);
    this.profitLossService.getPCProfitAndLossValues(model).subscribe({
      next: (result) => {
        if (result?.rows?.length > 0) {
          this.setCurrentPeriodType(
            result.isMonthly,
            result.isQuarterly,
            result.isAnnually
          );
          this.currentValueType = this.valueType;
          this.setValueType(
            result.isMonthly,
            result.isQuarterly,
            result.isAnnually
          );
          this.isPageLoad = false;
          if (this.isYtd) this.isYtdPageLoad = false;
          if (this.isLtm) this.isLtmPageLoad = false;
          this.isLoader = false;
          this.tableColumns = result?.headers || [];
          this.tableResult = result?.rows || [];
          this.tableResultClone = result?.rows || [];
          this.convertUnits();
          this.auditLogList = result?.financialKpiAuditlog || [];
          this.tableFrozenColumns = this.frozenHeader;
        } else {
          this.isLoader = false;
          this.currentPeriodType = undefined;
          this.currentValueType = undefined;
          this.resetTable();
        }
      },
      error: (error) => {
        this.resetTable();
        this.isLoader = false;
        this.currentPeriodType = undefined;
        this.currentValueType = undefined;
      },
    });
  }

  setCurrentPeriodType(
    isMonthly: boolean,
    isQuarterly: boolean,
    isAnnually: boolean
  ) {
    switch (true) {
      case isMonthly:
        this.currentPeriodType = PeriodType.Monthly;
        break;
      case isQuarterly:
        this.currentPeriodType = PeriodType.Quarterly;
        break;
      case isAnnually:
        this.currentPeriodType = PeriodType.Annually;
        break;
    }
  }

  resetTable() {
    this.isLoader = false;
    this.tableResult = [];
    this.tableResultClone = [];
    this.tableFrozenColumns = [];
    this.auditLogList = [];
  }
  setValueType(isMonthly: boolean, isQuarterly: boolean, isAnnually: boolean) {
    let valueTypes: any = {
      isMonthly: isMonthly,
      isQuarterly: isQuarterly,
      isAnnually: isAnnually,
    };
    this.onChangeValueType.emit(valueTypes);
  }
  getDataModel(event: any, searchFilter: any) {
    return {
      CompanyId: this.model.portfolioCompany.portfolioCompanyID,
      paginationFilter: event,
      searchFilter: searchFilter,
      segmentType: null,
      currencyCode: this.model?.currencyCode?.currencyCode,
      reportingCurrencyCode: this.model.portfolioCompany.companyCurrency,
      currencyRateSource: this.model?.fxRates?.type,
      valueType:
        this.valueTypeString != undefined
          ? this.valueTypeString
          : this.valueType,
      isMonthly: this.periodType == PeriodType.Monthly ? true : false,
      isQuarterly: this.periodType == PeriodType.Quarterly ? true : false,
      isAnnually: this.periodType == PeriodType.Annually ? true : false,
      PeriodType: this.periodType,
      ModuleId: 7,
      Kpis: null,
      isPageLoad: this.isPageLoad,
      isYtdPageLoad: this.isYtdPageLoad,
      isLtmPageLoad: this.isLtmPageLoad,
      kpiConfigurationData: this.pageConfigData.kpiConfigurationData,
      isYtd: this.isYtd,
      isLtm: this.isLtm,
      IsSpotRate : this.model.isSpotRate == null ? false : this.model.isSpotRate,
      SpotRateDate:this.model.isSpotRate ? this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd') : null
    };
  }
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
  exportProfitAndLossData() {
    this.isDownloading.emit(true);
    let event = {
      first: 0,
      rows: 1000,
      globalFilter: null,
      sortField: "displayOrder",
      multiSortMeta: this.profitAndLossValuesMultiSortMeta,
      sortOrder: -1,
    };
    this.profitLossService
      .exportCompanyProfitAndLoss({
        currency: this.model.portfolioCompany.companyCurrency,
        currencyCode: this.model?.currencyCode?.currencyCode,
        reportingCurrencyCode: this.model.portfolioCompany.companyCurrency,
        CompanyId: this.model.portfolioCompany.portfolioCompanyID,
        paginationFilter: event,
        segmentType: null,
        searchFilter: this.profitAndLossSearchFilter,
        currencyRateSource: this.model?.fxRates?.type,
        valueType: this.valueType,
        isMonthly: this.periodType == PeriodType.Monthly ? true : false,
        isQuarterly: this.periodType == PeriodType.Quarterly ? true : false,
        isAnnually: this.periodType == PeriodType.Annually ? true : false,
        ModuleId: 7,
        Kpis: null,
        isPageLoad: this.isPageLoad,
        kpiConfigurationData: this.pageConfigData.kpiConfigurationData,
        unit:this.model.currecyUnit.typeId,
        IsSpotRate : this.model.isSpotRate == null ? false : this.model.isSpotRate,
        SpotRateDate:this.model.isSpotRate ? this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd') : null,
        SpotRate:this.model.isSpotRate ? this.model.spotRate : null
      })
      .subscribe({
        next: (response: HttpResponse<Blob>) => {
          this.miscService.downloadExcelFile(response);
          this.isDownloading.emit(false);
        },
        error: (error: any) => {
          this.isDownloading.emit(false);
        },
      });
  }
  getFilterAuditValue(rowdata: any, column: any) {
    let calccolumn = "Calc" + " " + column.field;
    if (rowdata[calccolumn] != undefined && rowdata[calccolumn]) {
      return [];
    } else {
      let headers = column.field.split(" ");
      let auditList = this.auditLogList;
      let periodHeader = null;
      let yearHeader = null;
      let monthValue = null;
      if (headers?.length > 0 && auditList?.length > 0) {
        if (headers.length == 1) yearHeader = parseInt(headers[0]);
        else {
          periodHeader = headers[0];
          yearHeader = parseInt(headers[1]);
          if (!periodHeader.toLowerCase().includes("q"))
            monthValue = this.miscService.getMonthNumber(periodHeader);
        }
      }
      return this.filterAuditValue(
        yearHeader,
        monthValue,
        auditList,
        periodHeader,
        rowdata
      );
    }
  }
  filterAuditValue(
    yearHeader: any,
    monthValue: any,
    auditList: any,
    periodHeader: any,
    rowdata: any
  ) {
    let result = [];
    if (
      periodHeader == "Q1" ||
      periodHeader == "Q2" ||
      periodHeader == "Q3" ||
      periodHeader == "Q4"
    )
      result = auditList.filter(
        (x) =>
          x.quarter == periodHeader &&
          x.year == yearHeader &&
          x.mappingId == rowdata.MappingId &&
          x.kpiValueId > 0
      );
    else if (monthValue != null && monthValue > 0)
      result = auditList.filter(
        (x) =>
          x.month == monthValue &&
          x.year == yearHeader &&
          x.mappingId == rowdata.MappingId &&
          x.kpiValueId > 0
      );
    else
      result = auditList.filter(
        (x) =>
          (x.month == null || x.month == 0) &&
          x.year == yearHeader &&
          (x.Quarter == null || x.Quarter == "") &&
          x.mappingId == rowdata.MappingId &&
          x.kpiValueId > 0
      );
    return result;
  }
  printColumn(rowData: any, column: any) {
    let result = this.getFilterAuditValue(rowData, column);
    if (result.length > 0)
      return result[0].acutalAuditLog ? result[0].acutalAuditLog : false;
    else return false;
  }
  printCalcColumn(rowData: any, column: any) {
    let calColumn = "Calc" + " " + column.field;
    if (rowData[calColumn] != undefined && rowData[calColumn]) {
      return true;
    }
    return false;
  }

  /**
   * Handles the audit log functionality for a specific row and field.
   * @param rowData - The data of the row.
   * @param field - The field being audited.
   */
  onAuditLog(rowData: any, field: any) {
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (!this.isErrorLog || rowData.IsHeader) {
      if (this.isErrorLog) {
        this.showErrorToast(ERROR_MESSAGE);
      }
      return;
    }
    if (this.checkCalcColumn(rowData, field)) {
      const message = getConversionErrorMessage(
        this.model.isSpotRate,
        this.auditLogSpotRateConversionError,
        this.auditLogErrorForConvertedValue
      );
      this.toasterService.warning(message, '', { positionClass: "toast-center-center" });
      return;
    }
    const dateComponents = extractDateComponents(field.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
    this.auditService
      .getPortfolioEditSupportingCommentsData(auditLogFilter)
      .subscribe({
        next: (data: MappedDocuments) => {
          if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
            let attributeName = rowData.Kpi;
            this.redirectToAuditLogPage(
              field,
              attributeName,
              data,
              auditLogFilter
            );
          } else if (data?.auditLogCount == 0) {
            this.showErrorToast(GlobalConstants.AuditLogNAMessage);
          }
        },
        error: (error: any) => {
          this.showErrorToast(ERROR_MESSAGE);
        },
      });
  }

  /**
   * Redirects to the audit log page with the specified parameters.
   *
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents data.
   * @param auditLogFilter - The audit log filter.
   */
  redirectToAuditLogPage(
    field: any,
    attributeName: any,
    data: MappedDocuments,
    auditLogFilter: Audit
  ) {
    let params = {
      KPI: this.tabName,
      header: field.header,
      PortfolioCompanyID: this.model.portfolioCompany.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId: KPIModulesEnum.ProfitAndLoss,
      Comments:
        this.valueTypeString != undefined
          ? this.valueTypeString
          : this.valueType,
      currency: this.model.portfolioCompany.companyCurrency,
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
      MappingId: auditLogFilter.mappingId,
      SectionId: this.sectionId
    };
    sessionStorage.setItem(
      GlobalConstants.CurrentModule,
      KpiTypesConstants.PROFIT_LOSS_KPI
    );
    sessionStorage.setItem(
      GlobalConstants.ProfitandLossAuditLocalStorage,
      JSON.stringify(params)
    );
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl =
        config.redirect_uri.split("/in")[0] +
        GlobalConstants.FinancialsKpiAuditUrl;
      window.open(myAppUrl, "_blank");
    }
  }
  /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  convertUnits() {
    this.tableResult = [];
    let local = this;
    let masterValueUnit = local.model.currecyUnit;
    this.tableResultClone.forEach(function (value: any) {
      let valueClone = JSON.parse(JSON.stringify(value));
      if (
        valueClone["KpiInfo"] != "%" &&
        valueClone["KpiInfo"] != "x" &&
        valueClone["KpiInfo"] != "#" &&
        valueClone["KpiInfo"] != "Text" &&
        valueClone["KpiInfo"] != ""
      ) {
        switch (Number(masterValueUnit?.typeId)) {
          case FinancialValueUnitsEnum.Absolute:
            valueClone = local.conversionThousandToAbsoluteValue(
              valueClone,
              local,
              1000
            );
            break;
          case FinancialValueUnitsEnum.Thousands:
            break;
          case FinancialValueUnitsEnum.Millions:
            valueClone = local.conversionValue(valueClone, local, 1000000);
            break;
          case FinancialValueUnitsEnum.Billions:
            valueClone = local.conversionValue(valueClone, local, 1000000000);
            break;
        }
      }
      local.tableResult.push(valueClone);
    });
  }
  /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  conversionValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] = !isNil(valueClone[col.field])
          ? !isNaN(
            parseFloat(
              valueClone[col.field].indexOf(",") > -1
                ? valueClone[col.field].replace(/,/g, "")
                : valueClone[col.field]
            )
          )
            ? ((valueClone[col.field].indexOf(",") > -1
              ? valueClone[col.field].replace(/,/g, "")
              : valueClone[col.field]) * 1000) / value
            : valueClone[col.field]
          : valueClone[col.field];
      }
    });
    return valueClone;
  }
  /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  conversionThousandToAbsoluteValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] = !isNil(valueClone[col.field])
          ? !isNaN(
            parseFloat(
              valueClone[col.field].indexOf(",") > -1
                ? valueClone[col.field].replace(/,/g, "")
                : valueClone[col.field]
            )
          )
            ? (valueClone[col.field].indexOf(",") > -1
              ? valueClone[col.field].replace(/,/g, "")
              : valueClone[col.field]) * value
            : valueClone[col.field]
          : valueClone[col.field];
      }
    });
    return valueClone;
  }
  /**
   * Returns an Audit object representing the filter criteria for the audit log.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components (year, month, quarter).
   * @returns An Audit object with the filter criteria.
   */
  getAuditLogFilter(
    rowData: any,
    dateComponents: { year: any; month: number; quarter: any }
  ) {
    return <Audit>{
      valueType:
        this.valueTypeString != undefined
          ? this.valueTypeString
          : this.valueType,
      kpiId: rowData["LineItemId"],
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: KPIModulesEnum.ProfitAndLoss,
      companyId: this.model.portfolioCompany.portfolioCompanyID,
    };
  }

  getValues(rowdata: any, column: any) {
    let result = this.getFilterAuditValue(rowdata, column);
    if (result.length > 0) {
      return result[0];
    } else return null;
  }
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: ModuleCompanyModel;
  dataRow: object;
  dataColumns: TableHeader;
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }
  /**
   * Initializes the edit mode for a specific row and column in the profit-loss-beta component.
   *
   * @param rowData - The data of the row being edited.
   * @param column - The column being edited.
   */
  onEditInit(rowData: any, column: any) {
    if (!this.plEditPermission) {
      this.showNoAccessError();
      return;
    }
    if (this.checkCalcColumn(rowData, column) && !this.isErrorLog) {
      const message = getConversionErrorMessage(
        this.model.isSpotRate,
        this.editSpotRateConversionError,
        this.editErrorForConvertedValue
      );
      this.toasterService.warning(message, '', { positionClass: "toast-center-center" });
      return;
    }
    if (!this.isErrorLog) {
      if (!rowData.IsHeader && this.model.currecyUnit.typeId == FinancialValueUnitsEnum.Thousands) {
        this.uniqueModuleCompany = {
          moduleId: this.kpiModuleId,
          companyId: this.model?.portfolioCompany?.portfolioCompanyID,
          valueType:
            this.valueTypeString != undefined
              ? this.valueTypeString
              : this.valueType,
          subPageId: 0,
        };
        this.dataRow = rowData;
        this.dataColumns = column;
        this.isUploadPopupVisible = true;
      } else {
        if (this.model.currecyUnit.typeId != FinancialValueUnitsEnum.Thousands) {
          this.infoUpdate = true;
        }
        else
          this.showErrorToast(GlobalConstants.FinancialsCellEditError);
      }
    }
  }
  /**
   * Checks if the specified column has a calculated value for the given row data.
   * @param rowData The data of the row.
   * @param column The column object.
   * @returns A boolean indicating whether the calculated column exists in the row data.
   */
  checkCalcColumn(rowData: any, column: any): boolean {
    let calColumn = `Calc ${column.field}`;
    return Boolean(rowData[calColumn]);
  }
  /**
   * Handles the event when the submit button is clicked.
   * @param results - The results of the submit action.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == "ok") {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getProfitLossData();
  }
  /**
   * Displays an error toast message.
   *
   * @param message - The error message to display.
   * @param title - The title of the error toast. (optional)
   * @param position - The position of the error toast. (optional)
   */
  showErrorToast(
    message: string,
    title: string = "",
    position: string = "toast-center-center"
  ): void {
    this.toasterService.error(message, title, { positionClass: position });
  }
  showNoAccessError() {
    this.toasterService.error(ErrorMessage.NoAccess, "", {
      positionClass: "toast-center-center",
    });
  }
  /**
   * Displays a success toast message.
   *
   * @param message - The message to be displayed in the toast.
   * @param title - The title of the toast (optional).
   * @param position - The position of the toast on the screen (optional, default is 'toast-center-center').
   */
  showSuccessToast(
    message: string,
    title: string = "",
    position: string = "toast-center-center"
  ): void {
    this.toasterService.success(message, title, { positionClass: position });
  }
}
