<div class="container-fluid">
  <div class="row align-items-center mt-3 header-container">
    <div class="col">
      <span class="Body-M text-neutral-gray-90">Add New Column</span>
    </div>
    <div class="col-auto ms-auto header-actions mt-2 mb-2">
      <app-kendo-button [passedClass]="'width-94'" name="cancel" type="Secondary">Cancel</app-kendo-button>
      <app-kendo-button [passedClass]="'width-94 mx-3'" name="save" type="Primary" [disabled]="!isCreateEnabled()">Save</app-kendo-button>
    </div>
  </div>
  <div class="row align-items-start">
    <div class="mb-4 mt-4" [ngClass]="isCreateEnabled() ? 'col-4' : 'col-6'">
      <div class="mb-2 Caption-M text-neutral-gray-80">Field Type</div>
      <kendo-combobox id="categoriesInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="fieldTypesOptions"
        [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Field Type"
        valueField="value" textField="text" [(ngModel)]="selectedFieldType">
      </kendo-combobox>
    </div>
    <div class="col-6" *ngIf="!isCreateEnabled()">
      <div class="d-flex align-items-center bg-light mr-3 mt-3 mb-3">
        <div class="flex-grow-1 pl-3">
          <div class="Body-M">Please Select the Nature of Your Field Type.</div>
          <div class="Caption-R text-neutral-gray-80">
            If you want to add static data, choose the Data option. If the data will be updated with each event, select
            the Time Series option.
          </div>
        </div>
        <img src="assets/dist/images/no-data-svg.svg" alt="info" class="no-data-svg" />
      </div>
    </div>
    <div *ngIf="isCreateEnabled()" class="col-4 mb-4 mt-4">
      <div class="mb-2 Caption-M text-neutral-gray-80">Name Pattern</div>
      <kendo-textbox id="namePatternInput" class="k-custom-solid-dropdown k-dropdown-height-32" placeholder="Enter Name Pattern"
        [(ngModel)]="namePattern">
      </kendo-textbox>
    </div>
    <div *ngIf="isCreateEnabled()" class="col-4 mb-4 mt-4">
      <div class="mb-2 Caption-M text-neutral-gray-80">Data Type</div>
      <kendo-combobox id="dataTypeInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="dataTypesOptions"
        [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Data Type"
        valueField="value" textField="text" [(ngModel)]="selectedDataType">
      </kendo-combobox>
    </div>
  </div>
  <div *ngIf="isCreateEnabled()" class="row align-items-start">
    <div *ngIf="selectedFieldType.value === 1" class="col-4 mb-4">
      <div class="mb-2 Caption-M text-neutral-gray-80">Tracking Frequency</div>
      <kendo-combobox id="dataTypeInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="trackingFrequencyOptions"
        [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Data Type"
        valueField="value" textField="text" [(ngModel)]="selectedTrackingFrequency">
      </kendo-combobox>
    </div>
    <div *ngIf="selectedFieldType.value === 1" class="col-4 mb-4">
      <div class="row m-0 p-0">
        <div class="col-6 pl-0">
          <div class="mb-2 Caption-M text-neutral-gray-80">From {{ trackingPeriodLabel() }}</div>
          <kendo-combobox id="dataTypeInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="dataTypes"
            [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Data Type"
            valueField="value" textField="text" [(ngModel)]="selectedDataType">
          </kendo-combobox>
        </div>

        <div class="col-6 pr-0">
          <div class="mb-2 Caption-M text-neutral-gray-80">To {{ trackingPeriodLabel() }}</div>
          <kendo-combobox id="dataTypeInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="dataTypes"
            [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Data Type"
            valueField="value" textField="text" [(ngModel)]="selectedDataType">
          </kendo-combobox>
        </div>
      </div>
    </div>
    <!-- <div class="col-4 mb-4">
      <div class="mb-2 Caption-M text-neutral-gray-80">Map To</div>
      <kendo-combobox id="dataTypeInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="dataTypes"
        [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Data Type"
        valueField="value" textField="text" [(ngModel)]="selectedDataType">
      </kendo-combobox>
    </div> -->
  </div>
</div>