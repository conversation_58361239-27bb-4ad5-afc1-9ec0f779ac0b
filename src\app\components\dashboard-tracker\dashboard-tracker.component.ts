import { Component, OnInit, Input } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridData: any[] = [];

  constructor(private dashboardTrackerService: DashboardTrackerService) {}

  ngOnInit(): void {
    this.dashboardTrackerService.getDashboardData().subscribe((data) => {
      this.gridData = data;
      this.isLoading = false;
    });
  }
}
