import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
declare let $: any;
@Injectable({
  providedIn: "root",
})
export class DataIngestionService {
  myAppUrl: string = "";
  constructor(
    private readonly http: HttpClient,
    @Inject("BASE_URL") baseUrl: string
  ) {
    this.myAppUrl = baseUrl;
  }
  errorHandler(error: any) {
    return throwError(() => error);
  }

  getDataExtractionTypes(featureId: number): Observable<any> {
    return this.http.get<any>(this.myAppUrl + `api/data-extraction-types/${featureId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getDataSourceTypes() {
    return this.http
      .get<any>(this.myAppUrl + "api/data-extraction-source-types")
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }

  addDocumentType(documentName: any): Observable<any> {
    return this.http
      .post<any>(this.myAppUrl + "api/add-document-type", documentName)
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }

  getKpiModulesPageConfigDetails(): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/data-ingestion/kpi-config")
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }
  getInvestmentDetails(): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/data-ingestion/fetch-funds")
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }
  getFundKpiDetails(): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/data-ingestion/fetch-fund-kpis")
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }
  getCompaniesKpiDetails(fundId:number): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + `api/data-ingestion/companies-and-kpis/${fundId}`)
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }
  getSubPageFieldsBySubPageId(subPageId: number): Observable<any> {
  return this.http
    .get<any>(this.myAppUrl + `api/page-config/subpage-fields/${subPageId}`)
    .pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
}
}